2025-07-16 19:01:32,614 20584 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:01:32,620 20584 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:01:32,620 20584 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:01:32,620 20584 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:01:32,621 20584 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:01:32,621 20584 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:01:32,749 20584 INFO ? odoo.sql_db: Connection to the database failed 
2025-07-16 19:02:00,234 21020 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:04:01,418 21628 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:08:02,443 22592 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:08:02,443 22592 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:08:02,444 22592 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:08:02,444 22592 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:08:02,444 22592 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:08:02,444 22592 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:08:03,188 22592 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-16 19:08:05,406 22592 INFO my_odoo_erp odoo.modules.loading: init db 
2025-07-16 19:08:08,072 22592 INFO my_odoo_erp odoo.modules.loading: loading 1 modules... 
2025-07-16 19:08:08,073 22592 INFO my_odoo_erp odoo.modules.loading: Loading module base (1/1) 
2025-07-16 19:08:08,103 22592 INFO my_odoo_erp odoo.modules.registry: module base: creating or updating database tables 
2025-07-16 19:08:08,688 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-07-16 19:08:08,689 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-07-16 19:08:08,689 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-07-16 19:08:08,891 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.user_id 
2025-07-16 19:08:08,891 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-07-16 19:08:08,891 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.complete_name 
2025-07-16 19:08:08,892 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.company_registry 
2025-07-16 19:08:08,892 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-07-16 19:08:08,892 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.partner_share 
2025-07-16 19:08:08,968 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.currency.decimal_places 
2025-07-16 19:08:09,016 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.uses_default_logo 
2025-07-16 19:08:09,016 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.logo_web 
2025-07-16 19:08:09,019 22592 INFO my_odoo_erp odoo.models: Computing parent_path for table res_company... 
2025-07-16 19:08:09,065 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.users.signature 
2025-07-16 19:08:09,065 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.users.share 
2025-07-16 19:08:11,618 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_bank.xml 
2025-07-16 19:08:11,643 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res.lang.csv 
2025-07-16 19:08:11,764 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-07-16 19:08:11,833 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-07-16 19:08:11,946 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-07-16 19:08:12,831 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_company_data.xml 
2025-07-16 19:08:12,846 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_users_data.xml 
2025-07-16 19:08:14,802 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-07-16 19:08:14,817 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_country_data.xml 
2025-07-16 19:08:15,607 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-07-16 19:08:15,622 22592 INFO my_odoo_erp odoo.modules.loading: loading base/security/base_groups.xml 
2025-07-16 19:08:15,922 22592 INFO my_odoo_erp odoo.modules.loading: loading base/security/base_security.xml 
2025-07-16 19:08:16,136 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/base_menus.xml 
2025-07-16 19:08:16,244 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-07-16 19:08:16,270 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_config_views.xml 
2025-07-16 19:08:16,282 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/res.country.state.csv 
2025-07-16 19:08:17,242 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-07-16 19:08:17,448 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-07-16 19:08:17,477 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-07-16 19:08:17,509 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-07-16 19:08:17,558 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-07-16 19:08:17,583 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-07-16 19:08:17,610 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-07-16 19:08:17,638 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-07-16 19:08:17,873 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-07-16 19:08:17,903 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-07-16 19:08:17,942 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-07-16 19:08:17,970 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-07-16 19:08:17,998 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-07-16 19:08:18,087 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-07-16 19:08:18,126 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-07-16 19:08:18,155 22592 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-07-16 19:08:18,166 22592 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-07-16 19:08:18,182 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-07-16 19:08:18,223 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-07-16 19:08:18,253 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-07-16 19:08:18,366 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-07-16 19:08:18,522 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-07-16 19:08:18,639 22592 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-07-16 19:08:18,644 22592 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-07-16 19:08:18,654 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-07-16 19:08:18,674 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-07-16 19:08:18,693 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-07-16 19:08:18,712 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-07-16 19:08:18,744 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-07-16 19:08:18,756 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-07-16 19:08:18,774 22592 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-07-16 19:08:18,795 22592 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-07-16 19:08:18,816 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-07-16 19:08:18,852 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_company_views.xml 
2025-07-16 19:08:18,887 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-07-16 19:08:18,919 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-07-16 19:08:19,069 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-07-16 19:08:19,111 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_country_views.xml 
2025-07-16 19:08:19,171 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-07-16 19:08:19,223 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_users_views.xml 
2025-07-16 19:08:19,378 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-07-16 19:08:19,390 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_property_views.xml 
2025-07-16 19:08:19,416 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-07-16 19:08:19,427 22592 INFO my_odoo_erp odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-07-16 19:08:19,467 22592 INFO my_odoo_erp odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-07-16 19:08:19,821 22592 INFO my_odoo_erp odoo.modules.loading: Module base loaded in 11.75s, 7941 queries (+7941 other) 
2025-07-16 19:08:19,821 22592 INFO my_odoo_erp odoo.modules.loading: 1 modules loaded in 11.75s, 7941 queries (+7941 extra) 
2025-07-16 19:08:19,851 22592 INFO my_odoo_erp odoo.modules.loading: updating modules list 
2025-07-16 19:08:19,854 22592 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-16 19:08:20,870 22592 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Invoicing'] to user __system__ #1 via n/a 
2025-07-16 19:08:21,145 22592 INFO my_odoo_erp odoo.modules.loading: loading 42 modules... 
2025-07-16 19:08:21,145 22592 INFO my_odoo_erp odoo.modules.loading: Loading module uom (2/42) 
2025-07-16 19:08:21,184 22592 INFO my_odoo_erp odoo.modules.registry: module uom: creating or updating database tables 
2025-07-16 19:08:21,331 22592 INFO my_odoo_erp odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-16 19:08:21,465 22592 INFO my_odoo_erp odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-16 19:08:21,503 22592 INFO my_odoo_erp odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-16 19:08:21,523 22592 INFO my_odoo_erp odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-16 19:08:21,595 22592 INFO my_odoo_erp odoo.modules.loading: Module uom loaded in 0.45s, 276 queries (+276 other) 
2025-07-16 19:08:21,595 22592 INFO my_odoo_erp odoo.modules.loading: Loading module web (3/42) 
2025-07-16 19:08:21,628 22592 INFO my_odoo_erp odoo.modules.registry: module web: creating or updating database tables 
2025-07-16 19:08:22,395 22592 INFO my_odoo_erp odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-07-16 19:08:22,409 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-07-16 19:08:22,486 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/report_templates.xml 
2025-07-16 19:08:22,605 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-07-16 19:08:22,620 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/partner_view.xml 
2025-07-16 19:08:22,628 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-07-16 19:08:22,636 22592 INFO my_odoo_erp odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-07-16 19:08:22,644 22592 INFO my_odoo_erp odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-07-16 19:08:22,653 22592 INFO my_odoo_erp odoo.modules.loading: loading web/data/report_layout.xml 
2025-07-16 19:08:22,729 22592 INFO my_odoo_erp odoo.modules.loading: Module web loaded in 1.13s, 1015 queries (+1015 other) 
2025-07-16 19:08:22,729 22592 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp (4/42) 
2025-07-16 19:08:22,820 22592 INFO my_odoo_erp odoo.modules.registry: module auth_totp: creating or updating database tables 
2025-07-16 19:08:22,928 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/security/security.xml 
2025-07-16 19:08:22,972 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-07-16 19:08:22,987 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-07-16 19:08:22,998 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-07-16 19:08:23,041 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-07-16 19:08:23,051 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-07-16 19:08:23,081 22592 INFO my_odoo_erp odoo.modules.loading: Module auth_totp loaded in 0.35s, 179 queries (+179 other) 
2025-07-16 19:08:23,081 22592 INFO my_odoo_erp odoo.modules.loading: Loading module base_import (5/42) 
2025-07-16 19:08:23,148 22592 INFO my_odoo_erp odoo.modules.registry: module base_import: creating or updating database tables 
2025-07-16 19:08:23,927 22592 INFO my_odoo_erp odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-07-16 19:08:23,955 22592 INFO my_odoo_erp odoo.modules.loading: Module base_import loaded in 0.87s, 827 queries (+827 other) 
2025-07-16 19:08:23,955 22592 INFO my_odoo_erp odoo.modules.loading: Loading module base_import_module (6/42) 
2025-07-16 19:08:24,006 22592 INFO my_odoo_erp odoo.modules.registry: module base_import_module: creating or updating database tables 
2025-07-16 19:08:24,107 22592 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-07-16 19:08:24,121 22592 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-07-16 19:08:24,149 22592 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-07-16 19:08:24,207 22592 INFO my_odoo_erp odoo.modules.loading: Module base_import_module loaded in 0.25s, 154 queries (+154 other) 
2025-07-16 19:08:24,208 22592 INFO my_odoo_erp odoo.modules.loading: Loading module base_setup (7/42) 
2025-07-16 19:08:24,247 22592 INFO my_odoo_erp odoo.modules.registry: module base_setup: creating or updating database tables 
2025-07-16 19:08:24,322 22592 INFO my_odoo_erp odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-07-16 19:08:24,328 22592 INFO my_odoo_erp odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-07-16 19:08:24,369 22592 INFO my_odoo_erp odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-07-16 19:08:24,396 22592 INFO my_odoo_erp odoo.modules.loading: Module base_setup loaded in 0.19s, 139 queries (+139 other) 
2025-07-16 19:08:24,396 22592 INFO my_odoo_erp odoo.modules.loading: Loading module bus (8/42) 
2025-07-16 19:08:24,476 22592 INFO my_odoo_erp odoo.modules.registry: module bus: creating or updating database tables 
2025-07-16 19:08:24,606 22592 INFO my_odoo_erp odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-07-16 19:08:24,638 22592 INFO my_odoo_erp odoo.modules.loading: Module bus loaded in 0.24s, 116 queries (+116 other) 
2025-07-16 19:08:24,638 22592 INFO my_odoo_erp odoo.modules.loading: Loading module http_routing (9/42) 
2025-07-16 19:08:24,686 22592 INFO my_odoo_erp odoo.modules.registry: module http_routing: creating or updating database tables 
2025-07-16 19:08:24,702 22592 INFO my_odoo_erp odoo.modules.loading: loading http_routing/views/http_routing_template.xml 
2025-07-16 19:08:24,751 22592 INFO my_odoo_erp odoo.modules.loading: loading http_routing/views/res_lang_views.xml 
2025-07-16 19:08:24,783 22592 INFO my_odoo_erp odoo.modules.loading: Module http_routing loaded in 0.15s, 71 queries (+71 other) 
2025-07-16 19:08:24,783 22592 INFO my_odoo_erp odoo.modules.loading: Loading module onboarding (10/42) 
2025-07-16 19:08:24,835 22592 INFO my_odoo_erp odoo.modules.registry: module onboarding: creating or updating database tables 
2025-07-16 19:08:25,045 22592 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_templates.xml 
2025-07-16 19:08:25,066 22592 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_views.xml 
2025-07-16 19:08:25,095 22592 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_menus.xml 
2025-07-16 19:08:25,110 22592 INFO my_odoo_erp odoo.modules.loading: loading onboarding/security/ir.model.access.csv 
2025-07-16 19:08:25,159 22592 INFO my_odoo_erp odoo.modules.loading: Module onboarding loaded in 0.38s, 237 queries (+237 other) 
2025-07-16 19:08:25,159 22592 INFO my_odoo_erp odoo.modules.loading: Loading module resource (11/42) 
2025-07-16 19:08:25,271 22592 INFO my_odoo_erp odoo.modules.registry: module resource: creating or updating database tables 
2025-07-16 19:08:25,508 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/data/resource_data.xml 
2025-07-16 19:08:25,542 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/security/ir.model.access.csv 
2025-07-16 19:08:25,568 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/security/resource_security.xml 
2025-07-16 19:08:25,605 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_resource_views.xml 
2025-07-16 19:08:25,646 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_leaves_views.xml 
2025-07-16 19:08:25,715 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_attendance_views.xml 
2025-07-16 19:08:25,734 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_views.xml 
2025-07-16 19:08:25,772 22592 INFO my_odoo_erp odoo.modules.loading: loading resource/views/menuitems.xml 
2025-07-16 19:08:25,840 22592 INFO my_odoo_erp odoo.modules.loading: Module resource loaded in 0.68s, 407 queries (+407 other) 
2025-07-16 19:08:25,840 22592 INFO my_odoo_erp odoo.modules.loading: Loading module web_tour (12/42) 
2025-07-16 19:08:25,886 22592 INFO my_odoo_erp odoo.modules.registry: module web_tour: creating or updating database tables 
2025-07-16 19:08:25,942 22592 INFO my_odoo_erp odoo.modules.loading: loading web_tour/security/ir.model.access.csv 
2025-07-16 19:08:25,962 22592 INFO my_odoo_erp odoo.modules.loading: loading web_tour/security/ir.rule.csv 
2025-07-16 19:08:25,982 22592 INFO my_odoo_erp odoo.modules.loading: loading web_tour/views/tour_views.xml 
2025-07-16 19:08:26,042 22592 INFO my_odoo_erp odoo.modules.loading: Module web_tour loaded in 0.20s, 104 queries (+104 other) 
2025-07-16 19:08:26,042 22592 INFO my_odoo_erp odoo.modules.loading: Loading module iap (13/42) 
2025-07-16 19:08:26,103 22592 INFO my_odoo_erp odoo.modules.registry: module iap: creating or updating database tables 
2025-07-16 19:08:26,234 22592 INFO my_odoo_erp odoo.modules.loading: loading iap/security/ir.model.access.csv 
2025-07-16 19:08:26,257 22592 INFO my_odoo_erp odoo.modules.loading: loading iap/security/ir_rule.xml 
2025-07-16 19:08:26,271 22592 INFO my_odoo_erp odoo.modules.loading: loading iap/views/iap_views.xml 
2025-07-16 19:08:26,302 22592 INFO my_odoo_erp odoo.modules.loading: loading iap/views/res_config_settings.xml 
2025-07-16 19:08:26,335 22592 INFO my_odoo_erp odoo.modules.loading: Module iap loaded in 0.29s, 151 queries (+151 other) 
2025-07-16 19:08:26,335 22592 INFO my_odoo_erp odoo.modules.loading: Loading module mail (14/42) 
2025-07-16 19:08:27,018 22592 INFO my_odoo_erp odoo.modules.registry: module mail: creating or updating database tables 
2025-07-16 19:08:27,446 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-16 19:08:27,446 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-16 19:08:27,447 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-16 19:08:27,447 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-16 19:08:27,448 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-16 19:08:27,448 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-16 19:08:27,448 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-16 19:08:27,449 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-16 19:08:27,449 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-16 19:08:27,449 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-16 19:08:27,449 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-16 19:08:27,449 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-16 19:08:27,526 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-16 19:08:27,526 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-16 19:08:27,553 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.users.notification_type 
2025-07-16 19:08:27,726 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.email_normalized 
2025-07-16 19:08:30,288 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_groups.xml 
2025-07-16 19:08:30,460 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_activity_schedule_views.xml 
2025-07-16 19:08:30,479 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_blacklist_remove_views.xml 
2025-07-16 19:08:30,491 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_compose_message_views.xml 
2025-07-16 19:08:30,528 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_resend_message_views.xml 
2025-07-16 19:08:30,549 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_resend_partner_views.xml 
2025-07-16 19:08:30,568 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_template_preview_views.xml 
2025-07-16 19:08:30,591 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_wizard_invite_views.xml 
2025-07-16 19:08:30,605 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_template_reset_views.xml 
2025-07-16 19:08:30,623 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/fetchmail_views.xml 
2025-07-16 19:08:30,670 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_subtype_views.xml 
2025-07-16 19:08:30,694 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_tracking_value_views.xml 
2025-07-16 19:08:30,714 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_notification_views.xml 
2025-07-16 19:08:30,734 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_views.xml 
2025-07-16 19:08:30,773 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_schedule_views.xml 
2025-07-16 19:08:30,796 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_mail_views.xml 
2025-07-16 19:08:30,835 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_followers_views.xml 
2025-07-16 19:08:30,852 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_ice_server_views.xml 
2025-07-16 19:08:30,870 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_member_views.xml 
2025-07-16 19:08:30,887 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_rtc_session_views.xml 
2025-07-16 19:08:30,909 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_link_preview_views.xml 
2025-07-16 19:08:30,928 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss/discuss_gif_favorite_views.xml 
2025-07-16 19:08:30,945 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_views.xml 
2025-07-16 19:08:30,983 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_shortcode_views.xml 
2025-07-16 19:08:31,008 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_views.xml 
2025-07-16 19:08:31,125 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_plan_views.xml 
2025-07-16 19:08:31,165 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_plan_template_views.xml 
2025-07-16 19:08:31,179 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_config_settings_views.xml 
2025-07-16 19:08:31,216 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/ir_config_parameter_data.xml 
2025-07-16 19:08:31,222 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/res_partner_data.xml 
2025-07-16 19:08:31,342 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_message_subtype_data.xml 
2025-07-16 19:08:31,359 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_chatter.xml 
2025-07-16 19:08:31,385 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_email_layouts.xml 
2025-07-16 19:08:31,414 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_mailgateway.xml 
2025-07-16 19:08:31,431 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/discuss_channel_data.xml 
2025-07-16 19:08:31,484 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_activity_data.xml 
2025-07-16 19:08:31,511 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/security_notifications_templates.xml 
2025-07-16 19:08:31,520 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/data/ir_cron_data.xml 
2025-07-16 19:08:31,589 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/security/mail_security.xml 
2025-07-16 19:08:31,805 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/security/ir.model.access.csv 
2025-07-16 19:08:31,908 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_public_templates.xml 
2025-07-16 19:08:31,916 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_alias_domain_views.xml 
2025-07-16 19:08:31,938 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_alias_views.xml 
2025-07-16 19:08:31,965 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_gateway_allowed_views.xml 
2025-07-16 19:08:31,981 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_guest_views.xml 
2025-07-16 19:08:31,998 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_reaction_views.xml 
2025-07-16 19:08:32,014 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_templates_public.xml 
2025-07-16 19:08:32,025 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_users_views.xml 
2025-07-16 19:08:32,049 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_users_settings_views.xml 
2025-07-16 19:08:32,065 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_template_views.xml 
2025-07-16 19:08:32,099 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/ir_actions_server_views.xml 
2025-07-16 19:08:32,113 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/ir_model_views.xml 
2025-07-16 19:08:32,143 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_partner_views.xml 
2025-07-16 19:08:32,209 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_blacklist_views.xml 
2025-07-16 19:08:32,234 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_menus.xml 
2025-07-16 19:08:32,424 22592 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_company_views.xml 
2025-07-16 19:08:32,580 22592 INFO my_odoo_erp odoo.modules.loading: Module mail loaded in 6.24s, 4140 queries (+4141 other) 
2025-07-16 19:08:32,580 22592 INFO my_odoo_erp odoo.modules.loading: Loading module web_editor (15/42) 
2025-07-16 19:08:32,780 22592 INFO my_odoo_erp odoo.modules.registry: module web_editor: creating or updating database tables 
2025-07-16 19:08:34,080 22592 INFO my_odoo_erp odoo.modules.loading: loading web_editor/security/ir.model.access.csv 
2025-07-16 19:08:34,109 22592 INFO my_odoo_erp odoo.modules.loading: loading web_editor/data/editor_assets.xml 
2025-07-16 19:08:34,118 22592 INFO my_odoo_erp odoo.modules.loading: loading web_editor/views/editor.xml 
2025-07-16 19:08:34,143 22592 INFO my_odoo_erp odoo.modules.loading: loading web_editor/views/snippets.xml 
2025-07-16 19:08:34,214 22592 INFO my_odoo_erp odoo.modules.loading: Module web_editor loaded in 1.63s, 1352 queries (+1352 other) 
2025-07-16 19:08:34,215 22592 INFO my_odoo_erp odoo.modules.loading: Loading module analytic (16/42) 
2025-07-16 19:08:34,318 22592 INFO my_odoo_erp odoo.modules.registry: module analytic: creating or updating database tables 
2025-07-16 19:08:34,629 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/analytic_security.xml 
2025-07-16 19:08:34,706 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/ir.model.access.csv 
2025-07-16 19:08:34,734 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_line_views.xml 
2025-07-16 19:08:34,787 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_account_views.xml 
2025-07-16 19:08:34,837 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_plan_views.xml 
2025-07-16 19:08:34,861 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_distribution_model_views.xml 
2025-07-16 19:08:34,878 22592 INFO my_odoo_erp odoo.modules.loading: loading analytic/data/analytic_data.xml 
2025-07-16 19:08:34,984 22592 INFO my_odoo_erp odoo.modules.loading: Module analytic loaded in 0.77s, 391 queries (+391 other) 
2025-07-16 19:08:34,984 22592 INFO my_odoo_erp odoo.modules.loading: Loading module auth_signup (17/42) 
2025-07-16 19:08:35,059 22592 INFO my_odoo_erp odoo.modules.registry: module auth_signup: creating or updating database tables 
2025-07-16 19:08:35,172 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/ir_config_parameter_data.xml 
2025-07-16 19:08:35,182 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/ir_cron_data.xml 
2025-07-16 19:08:35,201 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/mail_template_data.xml 
2025-07-16 19:08:35,222 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/res_config_settings_views.xml 
2025-07-16 19:08:35,243 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/res_users_views.xml 
2025-07-16 19:08:35,280 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/auth_signup_login_templates.xml 
2025-07-16 19:08:35,309 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/auth_signup_templates_email.xml 
2025-07-16 19:08:35,325 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/webclient_templates.xml 
2025-07-16 19:08:35,365 22592 INFO my_odoo_erp odoo.modules.loading: Module auth_signup loaded in 0.38s, 202 queries (+202 other) 
2025-07-16 19:08:35,365 22592 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp_mail (18/42) 
2025-07-16 19:08:35,430 22592 INFO my_odoo_erp odoo.modules.registry: module auth_totp_mail: creating or updating database tables 
2025-07-16 19:08:35,480 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/ir_action_data.xml 
2025-07-16 19:08:35,506 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/mail_template_data.xml 
2025-07-16 19:08:35,518 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/security_notifications_template.xml 
2025-07-16 19:08:35,531 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/views/res_users_views.xml 
2025-07-16 19:08:35,585 22592 INFO my_odoo_erp odoo.modules.loading: Module auth_totp_mail loaded in 0.22s, 112 queries (+112 other) 
2025-07-16 19:08:35,585 22592 INFO my_odoo_erp odoo.modules.loading: Loading module base_install_request (19/42) 
2025-07-16 19:08:35,656 22592 INFO my_odoo_erp odoo.modules.registry: module base_install_request: creating or updating database tables 
2025-07-16 19:08:35,784 22592 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/security/ir.model.access.csv 
2025-07-16 19:08:35,808 22592 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/wizard/base_module_install_request_views.xml 
2025-07-16 19:08:35,828 22592 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/data/mail_template_data.xml 
2025-07-16 19:08:35,836 22592 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/data/mail_templates_module_install.xml 
2025-07-16 19:08:35,843 22592 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/views/ir_module_module_views.xml 
2025-07-16 19:08:35,881 22592 INFO my_odoo_erp odoo.modules.loading: Module base_install_request loaded in 0.30s, 144 queries (+144 other) 
2025-07-16 19:08:35,881 22592 INFO my_odoo_erp odoo.modules.loading: Loading module google_gmail (20/42) 
2025-07-16 19:08:36,021 22592 INFO my_odoo_erp odoo.modules.registry: module google_gmail: creating or updating database tables 
2025-07-16 19:08:36,103 22592 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/fetchmail_server_views.xml 
2025-07-16 19:08:36,121 22592 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/ir_mail_server_views.xml 
2025-07-16 19:08:36,135 22592 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/res_config_settings_views.xml 
2025-07-16 19:08:36,186 22592 INFO my_odoo_erp odoo.modules.loading: Module google_gmail loaded in 0.30s, 118 queries (+118 other) 
2025-07-16 19:08:36,186 22592 INFO my_odoo_erp odoo.modules.loading: Loading module iap_mail (21/42) 
2025-07-16 19:08:36,242 22592 INFO my_odoo_erp odoo.modules.registry: module iap_mail: creating or updating database tables 
2025-07-16 19:08:36,272 22592 INFO my_odoo_erp odoo.modules.loading: loading iap_mail/data/mail_templates.xml 
2025-07-16 19:08:36,319 22592 INFO my_odoo_erp odoo.modules.loading: Module iap_mail loaded in 0.13s, 36 queries (+36 other) 
2025-07-16 19:08:36,319 22592 INFO my_odoo_erp odoo.modules.loading: Loading module mail_bot (22/42) 
2025-07-16 19:08:36,405 22592 INFO my_odoo_erp odoo.modules.registry: module mail_bot: creating or updating database tables 
2025-07-16 19:08:36,539 22592 INFO my_odoo_erp odoo.modules.loading: loading mail_bot/views/res_users_views.xml 
2025-07-16 19:08:36,570 22592 INFO my_odoo_erp odoo.modules.loading: loading mail_bot/data/mailbot_data.xml 
2025-07-16 19:08:36,596 22592 INFO my_odoo_erp odoo.modules.loading: Module mail_bot loaded in 0.28s, 161 queries (+161 other) 
2025-07-16 19:08:36,596 22592 INFO my_odoo_erp odoo.modules.loading: Loading module phone_validation (23/42) 
2025-07-16 19:08:36,723 22592 INFO my_odoo_erp odoo.modules.registry: module phone_validation: creating or updating database tables 
2025-07-16 19:08:38,489 22592 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/security/ir.model.access.csv 
2025-07-16 19:08:38,525 22592 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/views/phone_blacklist_views.xml 
2025-07-16 19:08:38,580 22592 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/wizard/phone_blacklist_remove_view.xml 
2025-07-16 19:08:38,616 22592 INFO my_odoo_erp odoo.modules.loading: Module phone_validation loaded in 2.02s, 1422 queries (+1422 other) 
2025-07-16 19:08:38,616 22592 INFO my_odoo_erp odoo.modules.loading: Loading module privacy_lookup (24/42) 
2025-07-16 19:08:38,703 22592 INFO my_odoo_erp odoo.modules.registry: module privacy_lookup: creating or updating database tables 
2025-07-16 19:08:38,949 22592 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/wizard/privacy_lookup_wizard_views.xml 
2025-07-16 19:08:39,005 22592 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/views/privacy_log_views.xml 
2025-07-16 19:08:39,043 22592 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/security/ir.model.access.csv 
2025-07-16 19:08:39,074 22592 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/data/ir_actions_server_data.xml 
2025-07-16 19:08:39,115 22592 INFO my_odoo_erp odoo.modules.loading: Module privacy_lookup loaded in 0.50s, 245 queries (+245 other) 
2025-07-16 19:08:39,115 22592 INFO my_odoo_erp odoo.modules.loading: Loading module product (25/42) 
2025-07-16 19:08:39,699 22592 INFO my_odoo_erp odoo.modules.registry: module product: creating or updating database tables 
2025-07-16 19:08:41,024 22592 INFO my_odoo_erp odoo.modules.loading: loading product/data/product_data.xml 
2025-07-16 19:08:41,058 22592 INFO my_odoo_erp odoo.modules.loading: loading product/security/product_security.xml 
2025-07-16 19:08:41,214 22592 INFO my_odoo_erp odoo.modules.loading: loading product/security/ir.model.access.csv 
2025-07-16 19:08:41,267 22592 INFO my_odoo_erp odoo.modules.loading: loading product/wizard/product_label_layout_views.xml 
2025-07-16 19:08:41,284 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_tag_views.xml 
2025-07-16 19:08:41,305 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_views.xml 
2025-07-16 19:08:41,439 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_config_settings_views.xml 
2025-07-16 19:08:41,470 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_attribute_views.xml 
2025-07-16 19:08:41,512 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_attribute_value_views.xml 
2025-07-16 19:08:41,519 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_category_views.xml 
2025-07-16 19:08:41,541 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_document_views.xml 
2025-07-16 19:08:41,570 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_packaging_views.xml 
2025-07-16 19:08:41,604 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_pricelist_item_views.xml 
2025-07-16 19:08:41,639 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_pricelist_views.xml 
2025-07-16 19:08:41,673 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_supplierinfo_views.xml 
2025-07-16 19:08:41,710 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_template_views.xml 
2025-07-16 19:08:41,760 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_country_group_views.xml 
2025-07-16 19:08:41,777 22592 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_partner_views.xml 
2025-07-16 19:08:41,799 22592 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_reports.xml 
2025-07-16 19:08:41,856 22592 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_product_templates.xml 
2025-07-16 19:08:41,911 22592 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_template_templates.xml 
2025-07-16 19:08:41,947 22592 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_packaging.xml 
2025-07-16 19:08:41,956 22592 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_pricelist_report_templates.xml 
2025-07-16 19:08:42,020 22592 INFO my_odoo_erp odoo.modules.loading: Module product loaded in 2.91s, 1483 queries (+1483 other) 
2025-07-16 19:08:42,021 22592 INFO my_odoo_erp odoo.modules.loading: Loading module web_unsplash (26/42) 
2025-07-16 19:08:42,115 22592 INFO my_odoo_erp odoo.modules.registry: module web_unsplash: creating or updating database tables 
2025-07-16 19:08:42,205 22592 INFO my_odoo_erp odoo.modules.loading: loading web_unsplash/views/res_config_settings_view.xml 
2025-07-16 19:08:42,265 22592 INFO my_odoo_erp odoo.modules.loading: Module web_unsplash loaded in 0.24s, 109 queries (+109 other) 
2025-07-16 19:08:42,265 22592 INFO my_odoo_erp odoo.modules.loading: Loading module partner_autocomplete (27/42) 
2025-07-16 19:08:42,366 22592 INFO my_odoo_erp odoo.modules.registry: module partner_autocomplete: creating or updating database tables 
2025-07-16 19:08:42,540 22592 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/security/ir.model.access.csv 
2025-07-16 19:08:42,561 22592 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_partner_views.xml 
2025-07-16 19:08:42,588 22592 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_company_views.xml 
2025-07-16 19:08:42,601 22592 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_config_settings_views.xml 
2025-07-16 19:08:42,622 22592 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/data/cron.xml 
2025-07-16 19:08:42,665 22592 INFO my_odoo_erp odoo.modules.loading: Module partner_autocomplete loaded in 0.40s, 196 queries (+196 other) 
2025-07-16 19:08:42,665 22592 INFO my_odoo_erp odoo.modules.loading: Loading module portal (28/42) 
2025-07-16 19:08:42,801 22592 INFO my_odoo_erp odoo.modules.registry: module portal: creating or updating database tables 
2025-07-16 19:08:43,201 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/security/ir.model.access.csv 
2025-07-16 19:08:43,221 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/data/mail_template_data.xml 
2025-07-16 19:08:43,232 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/data/mail_templates.xml 
2025-07-16 19:08:43,244 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/views/mail_templates_public.xml 
2025-07-16 19:08:43,255 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/views/portal_templates.xml 
2025-07-16 19:08:43,413 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/views/res_config_settings_views.xml 
2025-07-16 19:08:43,432 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/wizard/portal_share_views.xml 
2025-07-16 19:08:43,447 22592 INFO my_odoo_erp odoo.modules.loading: loading portal/wizard/portal_wizard_views.xml 
2025-07-16 19:08:43,501 22592 INFO my_odoo_erp odoo.modules.loading: Module portal loaded in 0.84s, 494 queries (+494 other) 
2025-07-16 19:08:43,501 22592 INFO my_odoo_erp odoo.modules.loading: Loading module sms (29/42) 
2025-07-16 19:08:43,681 22592 INFO my_odoo_erp odoo.modules.registry: module sms: creating or updating database tables 
2025-07-16 19:08:43,693 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.sms_template_id 
2025-07-16 19:08:43,693 22592 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.sms_method 
2025-07-16 19:08:43,744 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.phone_sanitized 
2025-07-16 19:08:45,631 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/data/ir_cron_data.xml 
2025-07-16 19:08:45,646 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_composer_views.xml 
2025-07-16 19:08:45,665 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_template_preview_views.xml 
2025-07-16 19:08:45,678 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_resend_views.xml 
2025-07-16 19:08:45,693 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_template_reset_views.xml 
2025-07-16 19:08:45,705 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/ir_actions_server_views.xml 
2025-07-16 19:08:45,725 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/mail_notification_views.xml 
2025-07-16 19:08:45,746 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/res_config_settings_views.xml 
2025-07-16 19:08:45,769 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/res_partner_views.xml 
2025-07-16 19:08:45,820 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/sms_sms_views.xml 
2025-07-16 19:08:45,871 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/views/sms_template_views.xml 
2025-07-16 19:08:45,908 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/security/ir.model.access.csv 
2025-07-16 19:08:45,955 22592 INFO my_odoo_erp odoo.modules.loading: loading sms/security/sms_security.xml 
2025-07-16 19:08:46,002 22592 INFO my_odoo_erp odoo.modules.loading: Module sms loaded in 2.50s, 1984 queries (+1984 other) 
2025-07-16 19:08:46,002 22592 INFO my_odoo_erp odoo.modules.loading: Loading module snailmail (30/42) 
2025-07-16 19:08:46,148 22592 INFO my_odoo_erp odoo.modules.registry: module snailmail: creating or updating database tables 
2025-07-16 19:08:46,602 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/data/snailmail_data.xml 
2025-07-16 19:08:46,619 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/views/report_assets.xml 
2025-07-16 19:08:46,639 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/views/snailmail_views.xml 
2025-07-16 19:08:46,668 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/wizard/snailmail_letter_format_error_views.xml 
2025-07-16 19:08:46,682 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/wizard/snailmail_letter_missing_required_fields_views.xml 
2025-07-16 19:08:46,694 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail/security/ir.model.access.csv 
2025-07-16 19:08:46,744 22592 INFO my_odoo_erp odoo.modules.loading: Module snailmail loaded in 0.74s, 433 queries (+433 other) 
2025-07-16 19:08:46,744 22592 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp_portal (31/42) 
2025-07-16 19:08:46,825 22592 INFO my_odoo_erp odoo.modules.registry: module auth_totp_portal: creating or updating database tables 
2025-07-16 19:08:46,885 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_portal/security/security.xml 
2025-07-16 19:08:46,915 22592 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_portal/views/templates.xml 
2025-07-16 19:08:46,943 22592 INFO my_odoo_erp odoo.modules.loading: Module auth_totp_portal loaded in 0.20s, 66 queries (+66 other) 
2025-07-16 19:08:46,944 22592 INFO my_odoo_erp odoo.modules.loading: Loading module digest (32/42) 
2025-07-16 19:08:47,051 22592 INFO my_odoo_erp odoo.modules.registry: module digest: creating or updating database tables 
2025-07-16 19:08:47,267 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/security/ir.model.access.csv 
2025-07-16 19:08:47,291 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/data/digest_data.xml 
2025-07-16 19:08:47,320 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/data/digest_tips_data.xml 
2025-07-16 19:08:47,349 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/data/ir_cron_data.xml 
2025-07-16 19:08:47,366 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/data/res_config_settings_data.xml 
2025-07-16 19:08:47,380 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/views/digest_views.xml 
2025-07-16 19:08:47,453 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/views/digest_templates.xml 
2025-07-16 19:08:47,463 22592 INFO my_odoo_erp odoo.modules.loading: loading digest/views/res_config_settings_views.xml 
2025-07-16 19:08:47,527 22592 INFO my_odoo_erp odoo.modules.loading: Module digest loaded in 0.58s, 296 queries (+296 other) 
2025-07-16 19:08:47,527 22592 INFO my_odoo_erp odoo.modules.loading: Loading module payment (33/42) 
2025-07-16 19:08:47,698 22592 INFO my_odoo_erp odoo.modules.registry: module payment: creating or updating database tables 
2025-07-16 19:08:48,465 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/data/onboarding_data.xml 
2025-07-16 19:08:48,474 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_method_data.xml 
2025-07-16 19:08:51,731 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_provider_data.xml 
2025-07-16 19:08:52,052 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_cron.xml 
2025-07-16 19:08:53,490 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/express_checkout_templates.xml 
2025-07-16 19:08:53,502 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_form_templates.xml 
2025-07-16 19:08:53,543 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/portal_templates.xml 
2025-07-16 19:08:53,617 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_provider_views.xml 
2025-07-16 19:08:53,661 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_method_views.xml 
2025-07-16 19:08:53,699 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_transaction_views.xml 
2025-07-16 19:08:53,739 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_token_views.xml 
2025-07-16 19:08:53,770 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/views/res_partner_views.xml 
2025-07-16 19:08:53,796 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/security/ir.model.access.csv 
2025-07-16 19:08:53,841 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/security/payment_security.xml 
2025-07-16 19:08:53,903 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_capture_wizard_views.xml 
2025-07-16 19:08:53,915 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_link_wizard_views.xml 
2025-07-16 19:08:53,930 22592 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_onboarding_views.xml 
2025-07-16 19:08:54,008 22592 INFO my_odoo_erp odoo.modules.loading: Module payment loaded in 6.48s, 2210 queries (+2210 other) 
2025-07-16 19:08:54,008 22592 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet (34/42) 
2025-07-16 19:08:54,147 22592 INFO my_odoo_erp odoo.modules.registry: module spreadsheet: creating or updating database tables 
2025-07-16 19:08:54,277 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet/views/public_readonly_spreadsheet_templates.xml 
2025-07-16 19:08:54,310 22592 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet loaded in 0.30s, 83 queries (+83 other) 
2025-07-16 19:08:54,310 22592 INFO my_odoo_erp odoo.modules.loading: Loading module account (35/42) 
2025-07-16 19:08:55,344 22592 INFO my_odoo_erp odoo.modules.registry: module account: creating or updating database tables 
2025-07-16 19:08:56,141 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.account_fiscal_country_id 
2025-07-16 19:08:56,141 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.company.invoice_terms_html 
2025-07-16 19:09:00,215 22592 INFO my_odoo_erp odoo.modules.loading: loading account/security/account_security.xml 
2025-07-16 19:09:00,994 22592 INFO my_odoo_erp odoo.modules.loading: loading account/security/ir.model.access.csv 
2025-07-16 19:09:01,209 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_data.xml 
2025-07-16 19:09:01,356 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/digest_data.xml 
2025-07-16 19:09:01,370 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_report.xml 
2025-07-16 19:09:01,411 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/mail_template_data.xml 
2025-07-16 19:09:01,444 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/onboarding_data.xml 
2025-07-16 19:09:01,560 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_view.xml 
2025-07-16 19:09:01,687 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_automatic_entry_wizard_views.xml 
2025-07-16 19:09:01,702 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_unreconcile_view.xml 
2025-07-16 19:09:01,717 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_reversal_view.xml 
2025-07-16 19:09:01,732 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_resequence_views.xml 
2025-07-16 19:09:01,747 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_payment_register_views.xml 
2025-07-16 19:09:01,763 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_move_views.xml 
2025-07-16 19:09:02,439 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/setup_wizards_view.xml 
2025-07-16 19:09:02,463 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_views.xml 
2025-07-16 19:09:02,504 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_group_views.xml 
2025-07-16 19:09:02,525 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_views.xml 
2025-07-16 19:09:02,607 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_tag_views.xml 
2025-07-16 19:09:02,636 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_bank_statement_views.xml 
2025-07-16 19:09:02,688 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_reconcile_model_views.xml 
2025-07-16 19:09:02,737 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_tax_views.xml 
2025-07-16 19:09:02,840 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_full_reconcile_views.xml 
2025-07-16 19:09:02,849 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_term_views.xml 
2025-07-16 19:09:02,890 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_method.xml 
2025-07-16 19:09:02,907 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_partner_bank_views.xml 
2025-07-16 19:09:02,956 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_statement.xml 
2025-07-16 19:09:02,980 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/terms_template.xml 
2025-07-16 19:09:03,001 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_validate_move_view.xml 
2025-07-16 19:09:03,019 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_company_views.xml 
2025-07-16 19:09:03,065 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/product_view.xml 
2025-07-16 19:09:03,115 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_plan_views.xml 
2025-07-16 19:09:03,131 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_account_views.xml 
2025-07-16 19:09:03,158 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_distribution_model_views.xml 
2025-07-16 19:09:03,182 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_line_views.xml 
2025-07-16 19:09:03,238 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_invoice.xml 
2025-07-16 19:09:03,297 22592 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_invoice_report_view.xml 
2025-07-16 19:09:03,376 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_cash_rounding_view.xml 
2025-07-16 19:09:03,408 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/ir_module_views.xml 
2025-07-16 19:09:03,428 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_config_settings_views.xml 
2025-07-16 19:09:03,491 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/partner_view.xml 
2025-07-16 19:09:03,607 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_dashboard_view.xml 
2025-07-16 19:09:03,637 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_portal_templates.xml 
2025-07-16 19:09:03,692 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_payment_receipt_templates.xml 
2025-07-16 19:09:03,707 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/service_cron.xml 
2025-07-16 19:09:03,730 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_incoterms_view.xml 
2025-07-16 19:09:03,758 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_incoterms_data.xml 
2025-07-16 19:09:03,792 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/digest_views.xml 
2025-07-16 19:09:03,807 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_send_views.xml 
2025-07-16 19:09:03,827 22592 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_hash_integrity_templates.xml 
2025-07-16 19:09:03,837 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_currency.xml 
2025-07-16 19:09:03,850 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_menuitem.xml 
2025-07-16 19:09:04,334 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_tour_upload_bill.xml 
2025-07-16 19:09:04,356 22592 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/accrued_orders.xml 
2025-07-16 19:09:04,367 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/bill_preview_template.xml 
2025-07-16 19:09:04,384 22592 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_reports_data.xml 
2025-07-16 19:09:06,386 22592 INFO my_odoo_erp odoo.modules.loading: loading account/views/uom_uom_views.xml 
2025-07-16 19:09:06,749 22592 INFO my_odoo_erp odoo.modules.loading: Module account loaded in 12.44s, 5654 queries (+5654 other) 
2025-07-16 19:09:06,750 22592 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_dashboard (36/42) 
2025-07-16 19:09:07,034 22592 INFO my_odoo_erp odoo.modules.registry: module spreadsheet_dashboard: creating or updating database tables 
2025-07-16 19:09:07,352 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/security/security.xml 
2025-07-16 19:09:07,520 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/security/ir.model.access.csv 
2025-07-16 19:09:07,558 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/views/spreadsheet_dashboard_views.xml 
2025-07-16 19:09:07,596 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/views/menu_views.xml 
2025-07-16 19:09:07,674 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/data/dashboard.xml 
2025-07-16 19:09:07,750 22592 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_dashboard loaded in 1.00s, 269 queries (+269 other) 
2025-07-16 19:09:07,751 22592 INFO my_odoo_erp odoo.modules.loading: Loading module account_edi_ubl_cii (37/42) 
2025-07-16 19:09:08,385 22592 INFO my_odoo_erp odoo.modules.registry: module account_edi_ubl_cii: creating or updating database tables 
2025-07-16 19:09:08,423 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.ubl_cii_format 
2025-07-16 19:09:08,423 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.peppol_endpoint 
2025-07-16 19:09:08,423 22592 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.peppol_eas 
2025-07-16 19:09:08,853 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/cii_22_templates.xml 
2025-07-16 19:09:08,899 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_20_templates.xml 
2025-07-16 19:09:09,070 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_21_templates.xml 
2025-07-16 19:09:09,121 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_config_settings_views.xml 
2025-07-16 19:09:09,150 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_partner_views.xml 
2025-07-16 19:09:09,201 22592 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/wizard/account_move_send_views.xml 
2025-07-16 19:09:09,243 22592 INFO my_odoo_erp odoo.modules.loading: Module account_edi_ubl_cii loaded in 1.49s, 424 queries (+424 other) 
2025-07-16 19:09:09,244 22592 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment (38/42) 
2025-07-16 19:09:09,562 22592 INFO my_odoo_erp odoo.modules.registry: module account_payment: creating or updating database tables 
2025-07-16 19:09:10,005 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/ir_config_parameter.xml 
2025-07-16 19:09:10,015 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/onboarding_data.xml 
2025-07-16 19:09:10,045 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir.model.access.csv 
2025-07-16 19:09:10,078 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir_rules.xml 
2025-07-16 19:09:10,096 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_menus.xml 
2025-07-16 19:09:10,149 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_portal_templates.xml 
2025-07-16 19:09:10,213 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_move_views.xml 
2025-07-16 19:09:10,240 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_journal_views.xml 
2025-07-16 19:09:10,262 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_views.xml 
2025-07-16 19:09:10,280 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_provider_views.xml 
2025-07-16 19:09:10,295 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_transaction_views.xml 
2025-07-16 19:09:10,312 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/account_payment_register_views.xml 
2025-07-16 19:09:10,330 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_link_wizard_views.xml 
2025-07-16 19:09:10,342 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_refund_wizard_views.xml 
2025-07-16 19:09:10,355 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/res_config_settings_views.xml 
2025-07-16 19:09:10,471 22592 INFO my_odoo_erp odoo.modules.loading: Module account_payment loaded in 1.23s, 467 queries (+467 other) 
2025-07-16 19:09:10,471 22592 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment_term (39/42) 
2025-07-16 19:09:10,687 22592 INFO my_odoo_erp odoo.modules.registry: module account_payment_term: creating or updating database tables 
2025-07-16 19:09:10,763 22592 INFO my_odoo_erp odoo.modules.loading: loading account_payment_term/views/account_payment_term_views.xml 
2025-07-16 19:09:10,800 22592 INFO my_odoo_erp odoo.modules.loading: Module account_payment_term loaded in 0.33s, 52 queries (+52 other) 
2025-07-16 19:09:10,801 22592 INFO my_odoo_erp odoo.modules.loading: Loading module snailmail_account (40/42) 
2025-07-16 19:09:10,984 22592 INFO my_odoo_erp odoo.modules.registry: module snailmail_account: creating or updating database tables 
2025-07-16 19:09:11,161 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/views/res_config_settings_views.xml 
2025-07-16 19:09:11,197 22592 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/wizard/account_move_send_views.xml 
2025-07-16 19:09:11,240 22592 INFO my_odoo_erp odoo.modules.loading: Module snailmail_account loaded in 0.44s, 132 queries (+132 other) 
2025-07-16 19:09:11,240 22592 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_account (41/42) 
2025-07-16 19:09:11,410 22592 INFO my_odoo_erp odoo.modules.registry: module spreadsheet_account: creating or updating database tables 
2025-07-16 19:09:11,536 22592 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_account loaded in 0.30s, 55 queries (+55 other) 
2025-07-16 19:09:11,536 22592 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_dashboard_account (42/42) 
2025-07-16 19:09:11,700 22592 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard_account/data/dashboards.xml 
2025-07-16 19:09:11,801 22592 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.26s, 29 queries (+29 other) 
2025-07-16 19:09:11,801 22592 INFO my_odoo_erp odoo.modules.loading: 42 modules loaded in 50.66s, 26435 queries (+26436 extra) 
2025-07-16 19:09:13,505 22592 INFO my_odoo_erp odoo.modules.loading: Modules loaded. 
2025-07-16 19:09:14,406 22592 INFO my_odoo_erp odoo.modules.registry: Registry loaded in 69.110s 
2025-07-16 19:09:14,406 22592 INFO my_odoo_erp odoo.service.server: Initiating shutdown 
2025-07-16 19:09:14,406 22592 INFO my_odoo_erp odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-16 19:09:14,406 22592 INFO my_odoo_erp odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 1 connections  
2025-07-16 19:09:24,942 17512 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:09:24,944 17512 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:09:24,944 17512 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:09:24,944 17512 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:09:24,944 17512 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:09:24,945 17512 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:09:25,256 17512 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-16 19:09:26,032 17512 INFO my_odoo_erp odoo.modules.loading: loading 1 modules... 
2025-07-16 19:09:26,040 17512 INFO my_odoo_erp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-16 19:09:26,094 17512 INFO my_odoo_erp odoo.modules.loading: loading 42 modules... 
2025-07-16 19:09:26,660 17512 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-07-16 19:09:27,448 17512 INFO my_odoo_erp odoo.modules.loading: 42 modules loaded in 1.35s, 0 queries (+0 extra) 
2025-07-16 19:09:27,613 17512 INFO my_odoo_erp odoo.modules.loading: Modules loaded. 
2025-07-16 19:09:27,620 17512 INFO my_odoo_erp odoo.modules.registry: Registry loaded in 1.810s 
2025-07-16 19:12:10,030 18548 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:12:10,030 18548 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:12:10,031 18548 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:12:10,031 18548 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:12:10,032 18548 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:12:10,032 18548 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:12:10,392 18548 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-16 19:12:11,073 18548 INFO my_odoo_erp odoo.modules.loading: init db 
2025-07-16 19:12:13,793 18548 INFO my_odoo_erp odoo.modules.loading: loading 1 modules... 
2025-07-16 19:12:13,793 18548 INFO my_odoo_erp odoo.modules.loading: Loading module base (1/1) 
2025-07-16 19:12:13,825 18548 INFO my_odoo_erp odoo.modules.registry: module base: creating or updating database tables 
2025-07-16 19:12:14,420 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-07-16 19:12:14,420 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-07-16 19:12:14,421 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-07-16 19:12:14,644 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.user_id 
2025-07-16 19:12:14,644 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-07-16 19:12:14,644 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.complete_name 
2025-07-16 19:12:14,644 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.company_registry 
2025-07-16 19:12:14,644 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-07-16 19:12:14,645 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.partner_share 
2025-07-16 19:12:14,717 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.currency.decimal_places 
2025-07-16 19:12:14,765 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.uses_default_logo 
2025-07-16 19:12:14,765 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.logo_web 
2025-07-16 19:12:14,769 18548 INFO my_odoo_erp odoo.models: Computing parent_path for table res_company... 
2025-07-16 19:12:14,816 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.users.signature 
2025-07-16 19:12:14,817 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.users.share 
2025-07-16 19:12:17,572 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_bank.xml 
2025-07-16 19:12:17,583 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res.lang.csv 
2025-07-16 19:12:17,652 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-07-16 19:12:17,727 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-07-16 19:12:17,837 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-07-16 19:12:18,707 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_company_data.xml 
2025-07-16 19:12:18,722 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_users_data.xml 
2025-07-16 19:12:20,615 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-07-16 19:12:20,628 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res_country_data.xml 
2025-07-16 19:12:21,349 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-07-16 19:12:21,371 18548 INFO my_odoo_erp odoo.modules.loading: loading base/security/base_groups.xml 
2025-07-16 19:12:21,703 18548 INFO my_odoo_erp odoo.modules.loading: loading base/security/base_security.xml 
2025-07-16 19:12:21,898 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/base_menus.xml 
2025-07-16 19:12:22,031 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-07-16 19:12:22,054 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_config_views.xml 
2025-07-16 19:12:22,069 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/res.country.state.csv 
2025-07-16 19:12:23,046 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-07-16 19:12:23,233 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-07-16 19:12:23,263 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-07-16 19:12:23,289 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-07-16 19:12:23,336 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-07-16 19:12:23,370 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-07-16 19:12:23,411 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-07-16 19:12:23,448 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-07-16 19:12:23,699 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-07-16 19:12:23,732 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-07-16 19:12:23,776 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-07-16 19:12:23,807 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-07-16 19:12:23,844 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-07-16 19:12:23,917 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-07-16 19:12:23,946 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-07-16 19:12:23,963 18548 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-07-16 19:12:23,970 18548 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-07-16 19:12:23,980 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-07-16 19:12:24,005 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-07-16 19:12:24,023 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-07-16 19:12:24,095 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-07-16 19:12:24,238 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-07-16 19:12:24,431 18548 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-07-16 19:12:24,436 18548 INFO my_odoo_erp odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-07-16 19:12:24,445 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-07-16 19:12:24,462 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-07-16 19:12:24,482 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-07-16 19:12:24,501 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-07-16 19:12:24,530 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-07-16 19:12:24,542 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-07-16 19:12:24,562 18548 INFO my_odoo_erp odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-07-16 19:12:24,587 18548 INFO my_odoo_erp odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-07-16 19:12:24,617 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-07-16 19:12:24,668 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_company_views.xml 
2025-07-16 19:12:24,712 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-07-16 19:12:24,751 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-07-16 19:12:24,906 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-07-16 19:12:24,950 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_country_views.xml 
2025-07-16 19:12:25,010 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-07-16 19:12:25,064 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_users_views.xml 
2025-07-16 19:12:25,247 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-07-16 19:12:25,262 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/ir_property_views.xml 
2025-07-16 19:12:25,292 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-07-16 19:12:25,303 18548 INFO my_odoo_erp odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-07-16 19:12:25,340 18548 INFO my_odoo_erp odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-07-16 19:12:25,723 18548 INFO my_odoo_erp odoo.modules.loading: Module base loaded in 11.93s, 7941 queries (+7941 other) 
2025-07-16 19:12:25,723 18548 INFO my_odoo_erp odoo.modules.loading: 1 modules loaded in 11.93s, 7941 queries (+7941 extra) 
2025-07-16 19:12:25,757 18548 INFO my_odoo_erp odoo.modules.loading: updating modules list 
2025-07-16 19:12:25,761 18548 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-16 19:12:26,831 18548 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Invoicing'] to user __system__ #1 via n/a 
2025-07-16 19:12:27,116 18548 INFO my_odoo_erp odoo.modules.loading: loading 42 modules... 
2025-07-16 19:12:27,116 18548 INFO my_odoo_erp odoo.modules.loading: Loading module uom (2/42) 
2025-07-16 19:12:27,147 18548 INFO my_odoo_erp odoo.modules.registry: module uom: creating or updating database tables 
2025-07-16 19:12:27,278 18548 INFO my_odoo_erp odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-16 19:12:27,420 18548 INFO my_odoo_erp odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-16 19:12:27,466 18548 INFO my_odoo_erp odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-16 19:12:27,488 18548 INFO my_odoo_erp odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-16 19:12:27,563 18548 INFO my_odoo_erp odoo.modules.loading: Module uom loaded in 0.45s, 276 queries (+276 other) 
2025-07-16 19:12:27,563 18548 INFO my_odoo_erp odoo.modules.loading: Loading module web (3/42) 
2025-07-16 19:12:27,599 18548 INFO my_odoo_erp odoo.modules.registry: module web: creating or updating database tables 
2025-07-16 19:12:28,366 18548 INFO my_odoo_erp odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-07-16 19:12:28,379 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-07-16 19:12:28,460 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/report_templates.xml 
2025-07-16 19:12:28,576 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-07-16 19:12:28,590 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/partner_view.xml 
2025-07-16 19:12:28,599 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-07-16 19:12:28,606 18548 INFO my_odoo_erp odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-07-16 19:12:28,617 18548 INFO my_odoo_erp odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-07-16 19:12:28,630 18548 INFO my_odoo_erp odoo.modules.loading: loading web/data/report_layout.xml 
2025-07-16 19:12:28,724 18548 INFO my_odoo_erp odoo.modules.loading: Module web loaded in 1.16s, 1015 queries (+1015 other) 
2025-07-16 19:12:28,724 18548 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp (4/42) 
2025-07-16 19:12:28,785 18548 INFO my_odoo_erp odoo.modules.registry: module auth_totp: creating or updating database tables 
2025-07-16 19:12:28,878 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/security/security.xml 
2025-07-16 19:12:28,924 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-07-16 19:12:28,937 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-07-16 19:12:28,948 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-07-16 19:12:28,982 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-07-16 19:12:28,989 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-07-16 19:12:29,020 18548 INFO my_odoo_erp odoo.modules.loading: Module auth_totp loaded in 0.30s, 179 queries (+179 other) 
2025-07-16 19:12:29,020 18548 INFO my_odoo_erp odoo.modules.loading: Loading module base_import (5/42) 
2025-07-16 19:12:29,087 18548 INFO my_odoo_erp odoo.modules.registry: module base_import: creating or updating database tables 
2025-07-16 19:12:30,008 18548 INFO my_odoo_erp odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-07-16 19:12:30,037 18548 INFO my_odoo_erp odoo.modules.loading: Module base_import loaded in 1.02s, 827 queries (+827 other) 
2025-07-16 19:12:30,037 18548 INFO my_odoo_erp odoo.modules.loading: Loading module base_import_module (6/42) 
2025-07-16 19:12:30,077 18548 INFO my_odoo_erp odoo.modules.registry: module base_import_module: creating or updating database tables 
2025-07-16 19:12:30,178 18548 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-07-16 19:12:30,193 18548 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-07-16 19:12:30,220 18548 INFO my_odoo_erp odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-07-16 19:12:30,287 18548 INFO my_odoo_erp odoo.modules.loading: Module base_import_module loaded in 0.25s, 154 queries (+154 other) 
2025-07-16 19:12:30,288 18548 INFO my_odoo_erp odoo.modules.loading: Loading module base_setup (7/42) 
2025-07-16 19:12:30,325 18548 INFO my_odoo_erp odoo.modules.registry: module base_setup: creating or updating database tables 
2025-07-16 19:12:30,399 18548 INFO my_odoo_erp odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-07-16 19:12:30,405 18548 INFO my_odoo_erp odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-07-16 19:12:30,459 18548 INFO my_odoo_erp odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-07-16 19:12:30,486 18548 INFO my_odoo_erp odoo.modules.loading: Module base_setup loaded in 0.20s, 139 queries (+139 other) 
2025-07-16 19:12:30,486 18548 INFO my_odoo_erp odoo.modules.loading: Loading module bus (8/42) 
2025-07-16 19:12:30,536 18548 INFO my_odoo_erp odoo.modules.registry: module bus: creating or updating database tables 
2025-07-16 19:12:30,658 18548 INFO my_odoo_erp odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-07-16 19:12:30,696 18548 INFO my_odoo_erp odoo.modules.loading: Module bus loaded in 0.21s, 116 queries (+116 other) 
2025-07-16 19:12:30,696 18548 INFO my_odoo_erp odoo.modules.loading: Loading module http_routing (9/42) 
2025-07-16 19:12:30,728 18548 INFO my_odoo_erp odoo.modules.registry: module http_routing: creating or updating database tables 
2025-07-16 19:12:30,743 18548 INFO my_odoo_erp odoo.modules.loading: loading http_routing/views/http_routing_template.xml 
2025-07-16 19:12:30,789 18548 INFO my_odoo_erp odoo.modules.loading: loading http_routing/views/res_lang_views.xml 
2025-07-16 19:12:30,821 18548 INFO my_odoo_erp odoo.modules.loading: Module http_routing loaded in 0.12s, 71 queries (+71 other) 
2025-07-16 19:12:30,822 18548 INFO my_odoo_erp odoo.modules.loading: Loading module onboarding (10/42) 
2025-07-16 19:12:30,862 18548 INFO my_odoo_erp odoo.modules.registry: module onboarding: creating or updating database tables 
2025-07-16 19:12:31,080 18548 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_templates.xml 
2025-07-16 19:12:31,106 18548 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_views.xml 
2025-07-16 19:12:31,139 18548 INFO my_odoo_erp odoo.modules.loading: loading onboarding/views/onboarding_menus.xml 
2025-07-16 19:12:31,155 18548 INFO my_odoo_erp odoo.modules.loading: loading onboarding/security/ir.model.access.csv 
2025-07-16 19:12:31,205 18548 INFO my_odoo_erp odoo.modules.loading: Module onboarding loaded in 0.38s, 237 queries (+237 other) 
2025-07-16 19:12:31,206 18548 INFO my_odoo_erp odoo.modules.loading: Loading module resource (11/42) 
2025-07-16 19:12:31,263 18548 INFO my_odoo_erp odoo.modules.registry: module resource: creating or updating database tables 
2025-07-16 19:12:31,521 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/data/resource_data.xml 
2025-07-16 19:12:31,559 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/security/ir.model.access.csv 
2025-07-16 19:12:31,578 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/security/resource_security.xml 
2025-07-16 19:12:31,620 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_resource_views.xml 
2025-07-16 19:12:31,654 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_leaves_views.xml 
2025-07-16 19:12:31,711 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_attendance_views.xml 
2025-07-16 19:12:31,726 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/views/resource_calendar_views.xml 
2025-07-16 19:12:31,752 18548 INFO my_odoo_erp odoo.modules.loading: loading resource/views/menuitems.xml 
2025-07-16 19:12:31,801 18548 INFO my_odoo_erp odoo.modules.loading: Module resource loaded in 0.60s, 407 queries (+407 other) 
2025-07-16 19:12:31,801 18548 INFO my_odoo_erp odoo.modules.loading: Loading module web_tour (12/42) 
2025-07-16 19:12:31,836 18548 INFO my_odoo_erp odoo.modules.registry: module web_tour: creating or updating database tables 
2025-07-16 19:12:31,885 18548 INFO my_odoo_erp odoo.modules.loading: loading web_tour/security/ir.model.access.csv 
2025-07-16 19:12:31,901 18548 INFO my_odoo_erp odoo.modules.loading: loading web_tour/security/ir.rule.csv 
2025-07-16 19:12:31,916 18548 INFO my_odoo_erp odoo.modules.loading: loading web_tour/views/tour_views.xml 
2025-07-16 19:12:31,966 18548 INFO my_odoo_erp odoo.modules.loading: Module web_tour loaded in 0.16s, 104 queries (+104 other) 
2025-07-16 19:12:31,966 18548 INFO my_odoo_erp odoo.modules.loading: Loading module iap (13/42) 
2025-07-16 19:12:32,003 18548 INFO my_odoo_erp odoo.modules.registry: module iap: creating or updating database tables 
2025-07-16 19:12:32,110 18548 INFO my_odoo_erp odoo.modules.loading: loading iap/security/ir.model.access.csv 
2025-07-16 19:12:32,127 18548 INFO my_odoo_erp odoo.modules.loading: loading iap/security/ir_rule.xml 
2025-07-16 19:12:32,139 18548 INFO my_odoo_erp odoo.modules.loading: loading iap/views/iap_views.xml 
2025-07-16 19:12:32,171 18548 INFO my_odoo_erp odoo.modules.loading: loading iap/views/res_config_settings.xml 
2025-07-16 19:12:32,209 18548 INFO my_odoo_erp odoo.modules.loading: Module iap loaded in 0.24s, 151 queries (+151 other) 
2025-07-16 19:12:32,209 18548 INFO my_odoo_erp odoo.modules.loading: Loading module mail (14/42) 
2025-07-16 19:12:32,454 18548 INFO my_odoo_erp odoo.modules.registry: module mail: creating or updating database tables 
2025-07-16 19:12:32,846 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-16 19:12:32,846 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-16 19:12:32,846 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-16 19:12:32,846 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-16 19:12:32,846 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-16 19:12:32,847 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-16 19:12:32,908 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-16 19:12:32,908 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-16 19:12:32,929 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.users.notification_type 
2025-07-16 19:12:33,063 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.email_normalized 
2025-07-16 19:12:35,666 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_groups.xml 
2025-07-16 19:12:35,798 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_activity_schedule_views.xml 
2025-07-16 19:12:35,809 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_blacklist_remove_views.xml 
2025-07-16 19:12:35,816 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_compose_message_views.xml 
2025-07-16 19:12:35,842 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_resend_message_views.xml 
2025-07-16 19:12:35,855 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_resend_partner_views.xml 
2025-07-16 19:12:35,867 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_template_preview_views.xml 
2025-07-16 19:12:35,880 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_wizard_invite_views.xml 
2025-07-16 19:12:35,892 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/wizard/mail_template_reset_views.xml 
2025-07-16 19:12:35,903 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/fetchmail_views.xml 
2025-07-16 19:12:35,928 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_subtype_views.xml 
2025-07-16 19:12:35,944 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_tracking_value_views.xml 
2025-07-16 19:12:35,960 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_notification_views.xml 
2025-07-16 19:12:35,976 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_views.xml 
2025-07-16 19:12:36,013 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_schedule_views.xml 
2025-07-16 19:12:36,035 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_mail_views.xml 
2025-07-16 19:12:36,069 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_followers_views.xml 
2025-07-16 19:12:36,092 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_ice_server_views.xml 
2025-07-16 19:12:36,110 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_member_views.xml 
2025-07-16 19:12:36,129 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_rtc_session_views.xml 
2025-07-16 19:12:36,154 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_link_preview_views.xml 
2025-07-16 19:12:36,171 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss/discuss_gif_favorite_views.xml 
2025-07-16 19:12:36,187 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_channel_views.xml 
2025-07-16 19:12:36,223 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_shortcode_views.xml 
2025-07-16 19:12:36,248 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_views.xml 
2025-07-16 19:12:36,366 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_plan_views.xml 
2025-07-16 19:12:36,414 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_activity_plan_template_views.xml 
2025-07-16 19:12:36,431 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_config_settings_views.xml 
2025-07-16 19:12:36,470 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/ir_config_parameter_data.xml 
2025-07-16 19:12:36,480 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/res_partner_data.xml 
2025-07-16 19:12:36,628 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_message_subtype_data.xml 
2025-07-16 19:12:36,643 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_chatter.xml 
2025-07-16 19:12:36,670 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_email_layouts.xml 
2025-07-16 19:12:36,709 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_templates_mailgateway.xml 
2025-07-16 19:12:36,735 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/discuss_channel_data.xml 
2025-07-16 19:12:36,801 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/mail_activity_data.xml 
2025-07-16 19:12:36,835 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/security_notifications_templates.xml 
2025-07-16 19:12:36,844 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/data/ir_cron_data.xml 
2025-07-16 19:12:36,952 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/security/mail_security.xml 
2025-07-16 19:12:37,186 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/security/ir.model.access.csv 
2025-07-16 19:12:37,303 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/discuss_public_templates.xml 
2025-07-16 19:12:37,312 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_alias_domain_views.xml 
2025-07-16 19:12:37,334 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_alias_views.xml 
2025-07-16 19:12:37,359 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_gateway_allowed_views.xml 
2025-07-16 19:12:37,376 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_guest_views.xml 
2025-07-16 19:12:37,396 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_message_reaction_views.xml 
2025-07-16 19:12:37,416 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_templates_public.xml 
2025-07-16 19:12:37,428 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_users_views.xml 
2025-07-16 19:12:37,452 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_users_settings_views.xml 
2025-07-16 19:12:37,468 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_template_views.xml 
2025-07-16 19:12:37,513 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/ir_actions_server_views.xml 
2025-07-16 19:12:37,537 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/ir_model_views.xml 
2025-07-16 19:12:37,578 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_partner_views.xml 
2025-07-16 19:12:37,655 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_blacklist_views.xml 
2025-07-16 19:12:37,682 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/mail_menus.xml 
2025-07-16 19:12:37,942 18548 INFO my_odoo_erp odoo.modules.loading: loading mail/views/res_company_views.xml 
2025-07-16 19:12:38,125 18548 INFO my_odoo_erp odoo.modules.loading: Module mail loaded in 5.92s, 4140 queries (+4141 other) 
2025-07-16 19:12:38,125 18548 INFO my_odoo_erp odoo.modules.loading: Loading module web_editor (15/42) 
2025-07-16 19:12:38,260 18548 INFO my_odoo_erp odoo.modules.registry: module web_editor: creating or updating database tables 
2025-07-16 19:12:39,630 18548 INFO my_odoo_erp odoo.modules.loading: loading web_editor/security/ir.model.access.csv 
2025-07-16 19:12:39,662 18548 INFO my_odoo_erp odoo.modules.loading: loading web_editor/data/editor_assets.xml 
2025-07-16 19:12:39,670 18548 INFO my_odoo_erp odoo.modules.loading: loading web_editor/views/editor.xml 
2025-07-16 19:12:39,695 18548 INFO my_odoo_erp odoo.modules.loading: loading web_editor/views/snippets.xml 
2025-07-16 19:12:39,778 18548 INFO my_odoo_erp odoo.modules.loading: Module web_editor loaded in 1.65s, 1352 queries (+1352 other) 
2025-07-16 19:12:39,778 18548 INFO my_odoo_erp odoo.modules.loading: Loading module analytic (16/42) 
2025-07-16 19:12:39,844 18548 INFO my_odoo_erp odoo.modules.registry: module analytic: creating or updating database tables 
2025-07-16 19:12:40,180 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/analytic_security.xml 
2025-07-16 19:12:40,248 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/ir.model.access.csv 
2025-07-16 19:12:40,276 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_line_views.xml 
2025-07-16 19:12:40,336 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_account_views.xml 
2025-07-16 19:12:40,380 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_plan_views.xml 
2025-07-16 19:12:40,404 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_distribution_model_views.xml 
2025-07-16 19:12:40,424 18548 INFO my_odoo_erp odoo.modules.loading: loading analytic/data/analytic_data.xml 
2025-07-16 19:12:40,548 18548 INFO my_odoo_erp odoo.modules.loading: Module analytic loaded in 0.77s, 391 queries (+391 other) 
2025-07-16 19:12:40,548 18548 INFO my_odoo_erp odoo.modules.loading: Loading module auth_signup (17/42) 
2025-07-16 19:12:40,608 18548 INFO my_odoo_erp odoo.modules.registry: module auth_signup: creating or updating database tables 
2025-07-16 19:12:40,712 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/ir_config_parameter_data.xml 
2025-07-16 19:12:40,721 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/ir_cron_data.xml 
2025-07-16 19:12:40,742 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/data/mail_template_data.xml 
2025-07-16 19:12:40,767 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/res_config_settings_views.xml 
2025-07-16 19:12:40,792 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/res_users_views.xml 
2025-07-16 19:12:40,827 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/auth_signup_login_templates.xml 
2025-07-16 19:12:40,861 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/auth_signup_templates_email.xml 
2025-07-16 19:12:40,877 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_signup/views/webclient_templates.xml 
2025-07-16 19:12:40,922 18548 INFO my_odoo_erp odoo.modules.loading: Module auth_signup loaded in 0.37s, 202 queries (+202 other) 
2025-07-16 19:12:40,922 18548 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp_mail (18/42) 
2025-07-16 19:12:40,990 18548 INFO my_odoo_erp odoo.modules.registry: module auth_totp_mail: creating or updating database tables 
2025-07-16 19:12:41,039 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/ir_action_data.xml 
2025-07-16 19:12:41,067 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/mail_template_data.xml 
2025-07-16 19:12:41,077 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/data/security_notifications_template.xml 
2025-07-16 19:12:41,089 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_mail/views/res_users_views.xml 
2025-07-16 19:12:41,137 18548 INFO my_odoo_erp odoo.modules.loading: Module auth_totp_mail loaded in 0.21s, 112 queries (+112 other) 
2025-07-16 19:12:41,137 18548 INFO my_odoo_erp odoo.modules.loading: Loading module base_install_request (19/42) 
2025-07-16 19:12:41,207 18548 INFO my_odoo_erp odoo.modules.registry: module base_install_request: creating or updating database tables 
2025-07-16 19:12:41,321 18548 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/security/ir.model.access.csv 
2025-07-16 19:12:41,344 18548 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/wizard/base_module_install_request_views.xml 
2025-07-16 19:12:41,364 18548 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/data/mail_template_data.xml 
2025-07-16 19:12:41,374 18548 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/data/mail_templates_module_install.xml 
2025-07-16 19:12:41,381 18548 INFO my_odoo_erp odoo.modules.loading: loading base_install_request/views/ir_module_module_views.xml 
2025-07-16 19:12:41,423 18548 INFO my_odoo_erp odoo.modules.loading: Module base_install_request loaded in 0.29s, 144 queries (+144 other) 
2025-07-16 19:12:41,423 18548 INFO my_odoo_erp odoo.modules.loading: Loading module google_gmail (20/42) 
2025-07-16 19:12:41,561 18548 INFO my_odoo_erp odoo.modules.registry: module google_gmail: creating or updating database tables 
2025-07-16 19:12:41,636 18548 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/fetchmail_server_views.xml 
2025-07-16 19:12:41,652 18548 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/ir_mail_server_views.xml 
2025-07-16 19:12:41,666 18548 INFO my_odoo_erp odoo.modules.loading: loading google_gmail/views/res_config_settings_views.xml 
2025-07-16 19:12:41,713 18548 INFO my_odoo_erp odoo.modules.loading: Module google_gmail loaded in 0.29s, 118 queries (+118 other) 
2025-07-16 19:12:41,713 18548 INFO my_odoo_erp odoo.modules.loading: Loading module iap_mail (21/42) 
2025-07-16 19:12:41,796 18548 INFO my_odoo_erp odoo.modules.registry: module iap_mail: creating or updating database tables 
2025-07-16 19:12:41,830 18548 INFO my_odoo_erp odoo.modules.loading: loading iap_mail/data/mail_templates.xml 
2025-07-16 19:12:41,871 18548 INFO my_odoo_erp odoo.modules.loading: Module iap_mail loaded in 0.16s, 36 queries (+36 other) 
2025-07-16 19:12:41,871 18548 INFO my_odoo_erp odoo.modules.loading: Loading module mail_bot (22/42) 
2025-07-16 19:12:41,949 18548 INFO my_odoo_erp odoo.modules.registry: module mail_bot: creating or updating database tables 
2025-07-16 19:12:42,076 18548 INFO my_odoo_erp odoo.modules.loading: loading mail_bot/views/res_users_views.xml 
2025-07-16 19:12:42,107 18548 INFO my_odoo_erp odoo.modules.loading: loading mail_bot/data/mailbot_data.xml 
2025-07-16 19:12:42,129 18548 INFO my_odoo_erp odoo.modules.loading: Module mail_bot loaded in 0.26s, 161 queries (+161 other) 
2025-07-16 19:12:42,129 18548 INFO my_odoo_erp odoo.modules.loading: Loading module phone_validation (23/42) 
2025-07-16 19:12:42,220 18548 INFO my_odoo_erp odoo.modules.registry: module phone_validation: creating or updating database tables 
2025-07-16 19:12:43,876 18548 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/security/ir.model.access.csv 
2025-07-16 19:12:43,896 18548 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/views/phone_blacklist_views.xml 
2025-07-16 19:12:43,934 18548 INFO my_odoo_erp odoo.modules.loading: loading phone_validation/wizard/phone_blacklist_remove_view.xml 
2025-07-16 19:12:43,965 18548 INFO my_odoo_erp odoo.modules.loading: Module phone_validation loaded in 1.84s, 1422 queries (+1422 other) 
2025-07-16 19:12:43,966 18548 INFO my_odoo_erp odoo.modules.loading: Loading module privacy_lookup (24/42) 
2025-07-16 19:12:44,031 18548 INFO my_odoo_erp odoo.modules.registry: module privacy_lookup: creating or updating database tables 
2025-07-16 19:12:44,247 18548 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/wizard/privacy_lookup_wizard_views.xml 
2025-07-16 19:12:44,298 18548 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/views/privacy_log_views.xml 
2025-07-16 19:12:44,335 18548 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/security/ir.model.access.csv 
2025-07-16 19:12:44,362 18548 INFO my_odoo_erp odoo.modules.loading: loading privacy_lookup/data/ir_actions_server_data.xml 
2025-07-16 19:12:44,404 18548 INFO my_odoo_erp odoo.modules.loading: Module privacy_lookup loaded in 0.44s, 245 queries (+245 other) 
2025-07-16 19:12:44,404 18548 INFO my_odoo_erp odoo.modules.loading: Loading module product (25/42) 
2025-07-16 19:12:44,651 18548 INFO my_odoo_erp odoo.modules.registry: module product: creating or updating database tables 
2025-07-16 19:12:45,877 18548 INFO my_odoo_erp odoo.modules.loading: loading product/data/product_data.xml 
2025-07-16 19:12:45,912 18548 INFO my_odoo_erp odoo.modules.loading: loading product/security/product_security.xml 
2025-07-16 19:12:46,072 18548 INFO my_odoo_erp odoo.modules.loading: loading product/security/ir.model.access.csv 
2025-07-16 19:12:46,124 18548 INFO my_odoo_erp odoo.modules.loading: loading product/wizard/product_label_layout_views.xml 
2025-07-16 19:12:46,142 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_tag_views.xml 
2025-07-16 19:12:46,161 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_views.xml 
2025-07-16 19:12:46,302 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_config_settings_views.xml 
2025-07-16 19:12:46,336 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_attribute_views.xml 
2025-07-16 19:12:46,376 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_attribute_value_views.xml 
2025-07-16 19:12:46,382 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_category_views.xml 
2025-07-16 19:12:46,405 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_document_views.xml 
2025-07-16 19:12:46,431 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_packaging_views.xml 
2025-07-16 19:12:46,466 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_pricelist_item_views.xml 
2025-07-16 19:12:46,500 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_pricelist_views.xml 
2025-07-16 19:12:46,537 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_supplierinfo_views.xml 
2025-07-16 19:12:46,572 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/product_template_views.xml 
2025-07-16 19:12:46,632 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_country_group_views.xml 
2025-07-16 19:12:46,643 18548 INFO my_odoo_erp odoo.modules.loading: loading product/views/res_partner_views.xml 
2025-07-16 19:12:46,659 18548 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_reports.xml 
2025-07-16 19:12:46,700 18548 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_product_templates.xml 
2025-07-16 19:12:46,739 18548 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_template_templates.xml 
2025-07-16 19:12:46,763 18548 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_packaging.xml 
2025-07-16 19:12:46,771 18548 INFO my_odoo_erp odoo.modules.loading: loading product/report/product_pricelist_report_templates.xml 
2025-07-16 19:12:46,840 18548 INFO my_odoo_erp odoo.modules.loading: Module product loaded in 2.44s, 1483 queries (+1483 other) 
2025-07-16 19:12:46,840 18548 INFO my_odoo_erp odoo.modules.loading: Loading module web_unsplash (26/42) 
2025-07-16 19:12:46,921 18548 INFO my_odoo_erp odoo.modules.registry: module web_unsplash: creating or updating database tables 
2025-07-16 19:12:47,026 18548 INFO my_odoo_erp odoo.modules.loading: loading web_unsplash/views/res_config_settings_view.xml 
2025-07-16 19:12:47,079 18548 INFO my_odoo_erp odoo.modules.loading: Module web_unsplash loaded in 0.24s, 109 queries (+109 other) 
2025-07-16 19:12:47,079 18548 INFO my_odoo_erp odoo.modules.loading: Loading module partner_autocomplete (27/42) 
2025-07-16 19:12:47,190 18548 INFO my_odoo_erp odoo.modules.registry: module partner_autocomplete: creating or updating database tables 
2025-07-16 19:12:47,387 18548 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/security/ir.model.access.csv 
2025-07-16 19:12:47,405 18548 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_partner_views.xml 
2025-07-16 19:12:47,431 18548 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_company_views.xml 
2025-07-16 19:12:47,446 18548 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/views/res_config_settings_views.xml 
2025-07-16 19:12:47,467 18548 INFO my_odoo_erp odoo.modules.loading: loading partner_autocomplete/data/cron.xml 
2025-07-16 19:12:47,513 18548 INFO my_odoo_erp odoo.modules.loading: Module partner_autocomplete loaded in 0.43s, 196 queries (+196 other) 
2025-07-16 19:12:47,513 18548 INFO my_odoo_erp odoo.modules.loading: Loading module portal (28/42) 
2025-07-16 19:12:47,637 18548 INFO my_odoo_erp odoo.modules.registry: module portal: creating or updating database tables 
2025-07-16 19:12:48,031 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/security/ir.model.access.csv 
2025-07-16 19:12:48,049 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/data/mail_template_data.xml 
2025-07-16 19:12:48,059 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/data/mail_templates.xml 
2025-07-16 19:12:48,070 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/views/mail_templates_public.xml 
2025-07-16 19:12:48,079 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/views/portal_templates.xml 
2025-07-16 19:12:48,255 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/views/res_config_settings_views.xml 
2025-07-16 19:12:48,280 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/wizard/portal_share_views.xml 
2025-07-16 19:12:48,296 18548 INFO my_odoo_erp odoo.modules.loading: loading portal/wizard/portal_wizard_views.xml 
2025-07-16 19:12:48,356 18548 INFO my_odoo_erp odoo.modules.loading: Module portal loaded in 0.84s, 494 queries (+494 other) 
2025-07-16 19:12:48,357 18548 INFO my_odoo_erp odoo.modules.loading: Loading module sms (29/42) 
2025-07-16 19:12:48,490 18548 INFO my_odoo_erp odoo.modules.registry: module sms: creating or updating database tables 
2025-07-16 19:12:48,503 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.sms_template_id 
2025-07-16 19:12:48,503 18548 INFO my_odoo_erp odoo.models: Prepare computation of ir.actions.server.sms_method 
2025-07-16 19:12:48,553 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.phone_sanitized 
2025-07-16 19:12:50,502 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/data/ir_cron_data.xml 
2025-07-16 19:12:50,520 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_composer_views.xml 
2025-07-16 19:12:50,538 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_template_preview_views.xml 
2025-07-16 19:12:50,550 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_resend_views.xml 
2025-07-16 19:12:50,564 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/wizard/sms_template_reset_views.xml 
2025-07-16 19:12:50,577 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/ir_actions_server_views.xml 
2025-07-16 19:12:50,592 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/mail_notification_views.xml 
2025-07-16 19:12:50,610 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/res_config_settings_views.xml 
2025-07-16 19:12:50,641 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/res_partner_views.xml 
2025-07-16 19:12:50,689 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/sms_sms_views.xml 
2025-07-16 19:12:50,724 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/views/sms_template_views.xml 
2025-07-16 19:12:50,756 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/security/ir.model.access.csv 
2025-07-16 19:12:50,801 18548 INFO my_odoo_erp odoo.modules.loading: loading sms/security/sms_security.xml 
2025-07-16 19:12:50,845 18548 INFO my_odoo_erp odoo.modules.loading: Module sms loaded in 2.49s, 1984 queries (+1984 other) 
2025-07-16 19:12:50,845 18548 INFO my_odoo_erp odoo.modules.loading: Loading module snailmail (30/42) 
2025-07-16 19:12:50,944 18548 INFO my_odoo_erp odoo.modules.registry: module snailmail: creating or updating database tables 
2025-07-16 19:12:51,381 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/data/snailmail_data.xml 
2025-07-16 19:12:51,398 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/views/report_assets.xml 
2025-07-16 19:12:51,418 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/views/snailmail_views.xml 
2025-07-16 19:12:51,445 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/wizard/snailmail_letter_format_error_views.xml 
2025-07-16 19:12:51,458 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/wizard/snailmail_letter_missing_required_fields_views.xml 
2025-07-16 19:12:51,471 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail/security/ir.model.access.csv 
2025-07-16 19:12:51,540 18548 INFO my_odoo_erp odoo.modules.loading: Module snailmail loaded in 0.70s, 433 queries (+433 other) 
2025-07-16 19:12:51,540 18548 INFO my_odoo_erp odoo.modules.loading: Loading module auth_totp_portal (31/42) 
2025-07-16 19:12:51,728 18548 INFO my_odoo_erp odoo.modules.registry: module auth_totp_portal: creating or updating database tables 
2025-07-16 19:12:51,773 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_portal/security/security.xml 
2025-07-16 19:12:51,800 18548 INFO my_odoo_erp odoo.modules.loading: loading auth_totp_portal/views/templates.xml 
2025-07-16 19:12:51,828 18548 INFO my_odoo_erp odoo.modules.loading: Module auth_totp_portal loaded in 0.29s, 66 queries (+66 other) 
2025-07-16 19:12:51,828 18548 INFO my_odoo_erp odoo.modules.loading: Loading module digest (32/42) 
2025-07-16 19:12:51,953 18548 INFO my_odoo_erp odoo.modules.registry: module digest: creating or updating database tables 
2025-07-16 19:12:52,184 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/security/ir.model.access.csv 
2025-07-16 19:12:52,204 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/data/digest_data.xml 
2025-07-16 19:12:52,231 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/data/digest_tips_data.xml 
2025-07-16 19:12:52,261 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/data/ir_cron_data.xml 
2025-07-16 19:12:52,281 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/data/res_config_settings_data.xml 
2025-07-16 19:12:52,294 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/views/digest_views.xml 
2025-07-16 19:12:52,371 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/views/digest_templates.xml 
2025-07-16 19:12:52,381 18548 INFO my_odoo_erp odoo.modules.loading: loading digest/views/res_config_settings_views.xml 
2025-07-16 19:12:52,456 18548 INFO my_odoo_erp odoo.modules.loading: Module digest loaded in 0.63s, 296 queries (+296 other) 
2025-07-16 19:12:52,456 18548 INFO my_odoo_erp odoo.modules.loading: Loading module payment (33/42) 
2025-07-16 19:12:52,562 18548 INFO my_odoo_erp odoo.modules.registry: module payment: creating or updating database tables 
2025-07-16 19:12:53,220 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/data/onboarding_data.xml 
2025-07-16 19:12:53,230 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_method_data.xml 
2025-07-16 19:12:57,104 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_provider_data.xml 
2025-07-16 19:12:57,462 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/data/payment_cron.xml 
2025-07-16 19:13:00,217 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/express_checkout_templates.xml 
2025-07-16 19:13:00,230 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_form_templates.xml 
2025-07-16 19:13:00,266 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/portal_templates.xml 
2025-07-16 19:13:00,326 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_provider_views.xml 
2025-07-16 19:13:00,362 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_method_views.xml 
2025-07-16 19:13:00,402 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_transaction_views.xml 
2025-07-16 19:13:00,437 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/payment_token_views.xml 
2025-07-16 19:13:00,463 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/views/res_partner_views.xml 
2025-07-16 19:13:00,500 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/security/ir.model.access.csv 
2025-07-16 19:13:00,574 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/security/payment_security.xml 
2025-07-16 19:13:00,637 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_capture_wizard_views.xml 
2025-07-16 19:13:00,650 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_link_wizard_views.xml 
2025-07-16 19:13:00,660 18548 INFO my_odoo_erp odoo.modules.loading: loading payment/wizards/payment_onboarding_views.xml 
2025-07-16 19:13:00,719 18548 INFO my_odoo_erp odoo.modules.loading: Module payment loaded in 8.26s, 2210 queries (+2210 other) 
2025-07-16 19:13:00,719 18548 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet (34/42) 
2025-07-16 19:13:00,838 18548 INFO my_odoo_erp odoo.modules.registry: module spreadsheet: creating or updating database tables 
2025-07-16 19:13:00,928 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet/views/public_readonly_spreadsheet_templates.xml 
2025-07-16 19:13:00,968 18548 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet loaded in 0.25s, 83 queries (+83 other) 
2025-07-16 19:13:00,969 18548 INFO my_odoo_erp odoo.modules.loading: Loading module account (35/42) 
2025-07-16 19:13:01,290 18548 INFO my_odoo_erp odoo.modules.registry: module account: creating or updating database tables 
2025-07-16 19:13:01,918 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.account_fiscal_country_id 
2025-07-16 19:13:01,919 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.company.invoice_terms_html 
2025-07-16 19:13:05,579 18548 INFO my_odoo_erp odoo.modules.loading: loading account/security/account_security.xml 
2025-07-16 19:13:06,253 18548 INFO my_odoo_erp odoo.modules.loading: loading account/security/ir.model.access.csv 
2025-07-16 19:13:06,422 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_data.xml 
2025-07-16 19:13:06,546 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/digest_data.xml 
2025-07-16 19:13:06,557 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_report.xml 
2025-07-16 19:13:06,590 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/mail_template_data.xml 
2025-07-16 19:13:06,615 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/onboarding_data.xml 
2025-07-16 19:13:06,746 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_view.xml 
2025-07-16 19:13:06,858 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_automatic_entry_wizard_views.xml 
2025-07-16 19:13:06,873 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_unreconcile_view.xml 
2025-07-16 19:13:06,889 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_reversal_view.xml 
2025-07-16 19:13:06,905 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_resequence_views.xml 
2025-07-16 19:13:06,919 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_payment_register_views.xml 
2025-07-16 19:13:06,932 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_move_views.xml 
2025-07-16 19:13:07,483 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/setup_wizards_view.xml 
2025-07-16 19:13:07,504 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_views.xml 
2025-07-16 19:13:07,555 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_group_views.xml 
2025-07-16 19:13:07,579 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_views.xml 
2025-07-16 19:13:07,641 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_tag_views.xml 
2025-07-16 19:13:07,662 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_bank_statement_views.xml 
2025-07-16 19:13:07,700 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_reconcile_model_views.xml 
2025-07-16 19:13:07,739 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_tax_views.xml 
2025-07-16 19:13:07,830 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_full_reconcile_views.xml 
2025-07-16 19:13:07,838 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_term_views.xml 
2025-07-16 19:13:07,867 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_method.xml 
2025-07-16 19:13:07,880 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_partner_bank_views.xml 
2025-07-16 19:13:07,917 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_statement.xml 
2025-07-16 19:13:07,935 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/terms_template.xml 
2025-07-16 19:13:07,952 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_validate_move_view.xml 
2025-07-16 19:13:07,966 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_company_views.xml 
2025-07-16 19:13:08,001 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/product_view.xml 
2025-07-16 19:13:08,057 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_plan_views.xml 
2025-07-16 19:13:08,068 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_account_views.xml 
2025-07-16 19:13:08,087 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_distribution_model_views.xml 
2025-07-16 19:13:08,106 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_line_views.xml 
2025-07-16 19:13:08,152 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_invoice.xml 
2025-07-16 19:13:08,202 18548 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_invoice_report_view.xml 
2025-07-16 19:13:08,272 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_cash_rounding_view.xml 
2025-07-16 19:13:08,298 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/ir_module_views.xml 
2025-07-16 19:13:08,310 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_config_settings_views.xml 
2025-07-16 19:13:08,355 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/partner_view.xml 
2025-07-16 19:13:08,448 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_dashboard_view.xml 
2025-07-16 19:13:08,478 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_portal_templates.xml 
2025-07-16 19:13:08,531 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_payment_receipt_templates.xml 
2025-07-16 19:13:08,545 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/service_cron.xml 
2025-07-16 19:13:08,566 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_incoterms_view.xml 
2025-07-16 19:13:08,588 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_incoterms_data.xml 
2025-07-16 19:13:08,618 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/digest_views.xml 
2025-07-16 19:13:08,630 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_send_views.xml 
2025-07-16 19:13:08,648 18548 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_hash_integrity_templates.xml 
2025-07-16 19:13:08,657 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_currency.xml 
2025-07-16 19:13:08,671 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_menuitem.xml 
2025-07-16 19:13:09,027 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_tour_upload_bill.xml 
2025-07-16 19:13:09,050 18548 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/accrued_orders.xml 
2025-07-16 19:13:09,062 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/bill_preview_template.xml 
2025-07-16 19:13:09,079 18548 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_reports_data.xml 
2025-07-16 19:13:10,141 18548 INFO my_odoo_erp odoo.modules.loading: loading account/views/uom_uom_views.xml 
2025-07-16 19:13:10,389 18548 INFO my_odoo_erp odoo.modules.loading: Module account loaded in 9.42s, 5654 queries (+5654 other) 
2025-07-16 19:13:10,389 18548 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_dashboard (36/42) 
2025-07-16 19:13:10,577 18548 INFO my_odoo_erp odoo.modules.registry: module spreadsheet_dashboard: creating or updating database tables 
2025-07-16 19:13:10,793 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/security/security.xml 
2025-07-16 19:13:10,897 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/security/ir.model.access.csv 
2025-07-16 19:13:10,920 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/views/spreadsheet_dashboard_views.xml 
2025-07-16 19:13:10,945 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/views/menu_views.xml 
2025-07-16 19:13:10,991 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard/data/dashboard.xml 
2025-07-16 19:13:11,035 18548 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.65s, 269 queries (+269 other) 
2025-07-16 19:13:11,035 18548 INFO my_odoo_erp odoo.modules.loading: Loading module account_edi_ubl_cii (37/42) 
2025-07-16 19:13:11,311 18548 INFO my_odoo_erp odoo.modules.registry: module account_edi_ubl_cii: creating or updating database tables 
2025-07-16 19:13:11,343 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.ubl_cii_format 
2025-07-16 19:13:11,343 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.peppol_endpoint 
2025-07-16 19:13:11,343 18548 INFO my_odoo_erp odoo.models: Prepare computation of res.partner.peppol_eas 
2025-07-16 19:13:11,594 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/cii_22_templates.xml 
2025-07-16 19:13:11,629 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_20_templates.xml 
2025-07-16 19:13:11,764 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_21_templates.xml 
2025-07-16 19:13:11,811 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_config_settings_views.xml 
2025-07-16 19:13:11,846 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_partner_views.xml 
2025-07-16 19:13:11,888 18548 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/wizard/account_move_send_views.xml 
2025-07-16 19:13:11,926 18548 INFO my_odoo_erp odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.89s, 424 queries (+424 other) 
2025-07-16 19:13:11,926 18548 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment (38/42) 
2025-07-16 19:13:12,071 18548 INFO my_odoo_erp odoo.modules.registry: module account_payment: creating or updating database tables 
2025-07-16 19:13:12,374 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/ir_config_parameter.xml 
2025-07-16 19:13:12,382 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/onboarding_data.xml 
2025-07-16 19:13:12,412 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir.model.access.csv 
2025-07-16 19:13:12,434 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir_rules.xml 
2025-07-16 19:13:12,446 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_menus.xml 
2025-07-16 19:13:12,483 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_portal_templates.xml 
2025-07-16 19:13:12,529 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_move_views.xml 
2025-07-16 19:13:12,549 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_journal_views.xml 
2025-07-16 19:13:12,565 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_views.xml 
2025-07-16 19:13:12,579 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_provider_views.xml 
2025-07-16 19:13:12,590 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_transaction_views.xml 
2025-07-16 19:13:12,602 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/account_payment_register_views.xml 
2025-07-16 19:13:12,613 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_link_wizard_views.xml 
2025-07-16 19:13:12,622 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_refund_wizard_views.xml 
2025-07-16 19:13:12,631 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/res_config_settings_views.xml 
2025-07-16 19:13:12,707 18548 INFO my_odoo_erp odoo.modules.loading: Module account_payment loaded in 0.78s, 467 queries (+467 other) 
2025-07-16 19:13:12,707 18548 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment_term (39/42) 
2025-07-16 19:13:12,862 18548 INFO my_odoo_erp odoo.modules.registry: module account_payment_term: creating or updating database tables 
2025-07-16 19:13:12,921 18548 INFO my_odoo_erp odoo.modules.loading: loading account_payment_term/views/account_payment_term_views.xml 
2025-07-16 19:13:12,956 18548 INFO my_odoo_erp odoo.modules.loading: Module account_payment_term loaded in 0.25s, 52 queries (+52 other) 
2025-07-16 19:13:12,956 18548 INFO my_odoo_erp odoo.modules.loading: Loading module snailmail_account (40/42) 
2025-07-16 19:13:13,099 18548 INFO my_odoo_erp odoo.modules.registry: module snailmail_account: creating or updating database tables 
2025-07-16 19:13:13,280 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/views/res_config_settings_views.xml 
2025-07-16 19:13:13,332 18548 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/wizard/account_move_send_views.xml 
2025-07-16 19:13:13,378 18548 INFO my_odoo_erp odoo.modules.loading: Module snailmail_account loaded in 0.42s, 132 queries (+132 other) 
2025-07-16 19:13:13,378 18548 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_account (41/42) 
2025-07-16 19:13:13,607 18548 INFO my_odoo_erp odoo.modules.registry: module spreadsheet_account: creating or updating database tables 
2025-07-16 19:13:13,749 18548 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_account loaded in 0.37s, 55 queries (+55 other) 
2025-07-16 19:13:13,749 18548 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_dashboard_account (42/42) 
2025-07-16 19:13:14,022 18548 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard_account/data/dashboards.xml 
2025-07-16 19:13:14,128 18548 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.38s, 29 queries (+29 other) 
2025-07-16 19:13:14,128 18548 INFO my_odoo_erp odoo.modules.loading: 42 modules loaded in 47.01s, 26435 queries (+26436 extra) 
2025-07-16 19:13:15,419 18548 INFO my_odoo_erp odoo.modules.loading: Modules loaded. 
2025-07-16 19:13:16,342 18548 INFO my_odoo_erp odoo.modules.registry: Registry loaded in 65.357s 
2025-07-16 19:13:16,342 18548 INFO my_odoo_erp odoo.service.server: Initiating shutdown 
2025-07-16 19:13:16,342 18548 INFO my_odoo_erp odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-16 19:13:16,342 18548 INFO my_odoo_erp odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 1 connections  
2025-07-16 19:13:27,644 13928 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:13:27,645 13928 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:13:27,645 13928 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:13:27,646 13928 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:13:27,646 13928 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:13:27,646 13928 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:13:28,251 13928 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-16 19:13:29,287 13928 INFO my_odoo_erp odoo.modules.loading: loading 1 modules... 
2025-07-16 19:13:29,300 13928 INFO my_odoo_erp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-16 19:13:29,375 13928 INFO my_odoo_erp odoo.modules.loading: loading 42 modules... 
2025-07-16 19:13:30,050 13928 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-07-16 19:13:31,939 13928 INFO my_odoo_erp odoo.modules.loading: 42 modules loaded in 2.56s, 0 queries (+0 extra) 
2025-07-16 19:13:32,139 13928 INFO my_odoo_erp odoo.modules.loading: Modules loaded. 
2025-07-16 19:13:32,146 13928 INFO my_odoo_erp odoo.modules.registry: Registry loaded in 2.968s 
2025-07-16 19:14:32,425 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-07-16 19:14:32,588 13928 INFO my_odoo_erp odoo.addons.base.models.ir_attachment: filestore gc 375 checked, 0 removed 
2025-07-16 19:14:32,662 13928 INFO my_odoo_erp odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-07-16 19:14:32,704 13928 INFO my_odoo_erp odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-07-16 19:14:32,748 13928 INFO my_odoo_erp odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1] 
2025-07-16 19:14:32,871 13928 INFO my_odoo_erp odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-07-16 19:14:32,969 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (0.544s). 
2025-07-16 19:14:32,979 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Base: Portal Users Deletion`. 
2025-07-16 19:14:32,985 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Base: Portal Users Deletion` (0.006s). 
2025-07-16 19:14:32,998 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-16 19:14:33,009 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.011s). 
2025-07-16 19:14:33,017 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-07-16 19:14:33,027 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Notification: Delete Notifications older than 6 Month` (0.010s). 
2025-07-16 19:14:33,035 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-16 19:14:33,040 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.005s). 
2025-07-16 19:14:33,049 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-07-16 19:14:33,055 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Mail: send web push notification` (0.006s). 
2025-07-16 19:14:33,062 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-07-16 19:14:33,070 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Discuss: channel member unmute` (0.008s). 
2025-07-16 19:14:33,077 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-07-16 19:14:33,087 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Users: Notify About Unregistered Users` (0.010s). 
2025-07-16 19:14:33,094 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-16 19:14:33,098 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.004s). 
2025-07-16 19:14:33,105 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-16 19:14:33,110 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.004s). 
2025-07-16 19:14:33,120 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-16 19:14:33,127 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.006s). 
2025-07-16 19:14:33,135 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Starting job `Send invoices automatically`. 
2025-07-16 19:14:33,148 13928 INFO my_odoo_erp odoo.addons.base.models.ir_cron: Job done: `Send invoices automatically` (0.014s). 
2025-07-16 19:15:13,345 13928 INFO my_odoo_erp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-16 19:15:13,397 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:13] "GET / HTTP/1.1" 303 - 5 0.005 0.050
2025-07-16 19:15:13,658 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:13] "GET /web HTTP/1.1" 303 - 1 0.001 0.004
2025-07-16 19:15:14,515 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:14] "GET /web/login HTTP/1.1" 200 - 54 0.075 0.616
2025-07-16 19:15:18,184 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:18] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 2.793
2025-07-16 19:15:18,226 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:18] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.015 2.609
2025-07-16 19:15:18,478 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/9b4df13/web.assets_frontend_minimal.min.js (id:444) 
2025-07-16 19:15:18,558 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:18] "GET /web/assets/9b4df13/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 11 0.038 0.144
2025-07-16 19:15:26,223 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/10fbe12/web.assets_frontend.min.css (id:445) 
2025-07-16 19:15:26,240 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:26] "GET /web/assets/10fbe12/web.assets_frontend.min.css HTTP/1.1" 200 - 9 0.031 10.995
2025-07-16 19:15:27,801 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/3b59c44/web.assets_frontend_lazy.min.js (id:446) 
2025-07-16 19:15:28,217 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:28] "GET /web/assets/3b59c44/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 9 0.004 1.563
2025-07-16 19:15:28,399 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:28] "GET /website/translations/a0904ff8e2c6bfa6bb5d9f87c1ef6d45fe749aa6 HTTP/1.1" 200 - 13 0.014 0.018
2025-07-16 19:15:37,232 13928 INFO my_odoo_erp odoo.addons.base.models.res_users: Login successful for db:my_odoo_erp login:admin from 127.0.0.1 
2025-07-16 19:15:37,260 13928 INFO my_odoo_erp odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-07-16 19:15:37,261 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:37] "POST /web/login HTTP/1.1" 303 - 59 0.057 1.085
2025-07-16 19:15:37,867 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:37] "GET /web HTTP/1.1" 200 - 75 0.073 0.527
2025-07-16 19:15:38,409 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:38] "GET /web/webclient/load_menus/aade7e12ff1c0048d52ef4053d28193f070d7a999a58861a8d6610f26d93c4fc HTTP/1.1" 200 - 2 0.015 0.110
2025-07-16 19:15:38,438 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:38] "GET /web/webclient/translations/6a22baa2f7d5930a44215e118f1d7a2dcc4a8b1c?lang=en_US HTTP/1.1" 200 - 3 0.032 0.112
2025-07-16 19:15:46,406 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/547501a/web.assets_web.min.css (id:447) 
2025-07-16 19:15:46,657 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:46] "GET /web/assets/547501a/web.assets_web.min.css HTTP/1.1" 200 - 15 0.058 8.713
2025-07-16 19:15:49,620 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/6dc69ae/web.assets_web.min.js (id:448) 
2025-07-16 19:15:50,443 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:50] "GET /web/assets/6dc69ae/web.assets_web.min.js HTTP/1.1" 200 - 11 0.015 12.165
2025-07-16 19:15:51,252 13928 INFO my_odoo_erp odoo.addons.partner_autocomplete.models.res_company: Starting enrich of company My Company (1) 
2025-07-16 19:15:51,257 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 4 0.016 0.016
2025-07-16 19:15:51,285 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "POST /web/dataset/call_kw/res.company/iap_enrich_auto HTTP/1.1" 200 - 14 0.026 0.034
2025-07-16 19:15:51,312 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.024 0.022
2025-07-16 19:15:51,351 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.009
2025-07-16 19:15:51,354 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/6038f42/bus.websocket_worker_assets.min.js (id:449) 
2025-07-16 19:15:51,395 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 200 - 8 0.014 0.084
2025-07-16 19:15:51,540 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "POST /mail/init_messaging HTTP/1.1" 200 - 92 0.180 0.189
2025-07-16 19:15:51,643 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.003 0.008
2025-07-16 19:15:51,707 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.009
2025-07-16 19:15:51,761 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.005
2025-07-16 19:15:51,791 13928 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-16 19:15:51,890 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:15:51] "GET /web/offline HTTP/1.1" 200 - 6 0.008 0.020
2025-07-16 19:16:01,735 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:01] "POST /web/action/load HTTP/1.1" 200 - 6 0.009 0.018
2025-07-16 19:16:02,019 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:02] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.002
2025-07-16 19:16:02,050 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:02] "POST /mail/inbox/messages HTTP/1.1" 200 - 6 0.008 0.008
2025-07-16 19:16:02,143 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:02] "GET /web/image/res.partner/2/avatar_128?unique=2025-07-16%2019:13:10 HTTP/1.1" 200 - 7 0.004 0.014
2025-07-16 19:16:02,171 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:02] "GET /discuss/channel/1/avatar_128?unique=297f5de18e45aa65b263edb7919e11955bf3eda039e5241be174595dfe1bc1de3d010cdc22361797e1ed4942b7dd099e84e0983e0cb9e88e3b18f28731d057df HTTP/1.1" 200 - 4 0.008 0.013
2025-07-16 19:16:02,178 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:02] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 8 0.009 0.020
2025-07-16 19:16:06,820 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:06] "POST /web/action/load HTTP/1.1" 200 - 10 0.012 0.018
2025-07-16 19:16:07,240 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:07] "POST /web/dataset/call_kw/account.move/get_views HTTP/1.1" 200 - 93 0.059 0.148
2025-07-16 19:16:07,284 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:07] "POST /web/dataset/call_kw/account.move/web_search_read HTTP/1.1" 200 - 6 0.015 0.007
2025-07-16 19:16:07,575 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:07] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.001 0.003
2025-07-16 19:16:08,011 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "POST /onboarding/account_invoice HTTP/1.1" 200 - 34 0.041 0.068
2025-07-16 19:16:08,174 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "GET /web/static/img/smiling_face.svg HTTP/1.1" 200 - 0 0.000 0.008
2025-07-16 19:16:08,397 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "GET /web/image/onboarding.onboarding.step/2/step_image HTTP/1.1" 200 - 5 0.004 0.011
2025-07-16 19:16:08,445 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "GET /web/image/onboarding.onboarding.step/3/step_image HTTP/1.1" 200 - 4 0.004 0.013
2025-07-16 19:16:08,447 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "GET /web/image/onboarding.onboarding.step/11/step_image HTTP/1.1" 200 - 4 0.005 0.012
2025-07-16 19:16:08,451 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:08] "GET /web/image/onboarding.onboarding.step/4/step_image HTTP/1.1" 200 - 4 0.007 0.015
2025-07-16 19:16:17,023 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:17] "POST /web/dataset/call_kw/onboarding.onboarding.step/action_open_step_company_data HTTP/1.1" 200 - 3 0.001 0.004
2025-07-16 19:16:17,387 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:17] "POST /web/dataset/call_kw/res.company/get_views HTTP/1.1" 200 - 10 0.011 0.031
2025-07-16 19:16:17,641 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:17] "POST /web/dataset/call_kw/res.company/web_read HTTP/1.1" 200 - 16 0.014 0.025
2025-07-16 19:16:17,754 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:17] "POST /mail/thread/data HTTP/1.1" 200 - 17 0.018 0.024
2025-07-16 19:16:18,064 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:18] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.007 0.009
2025-07-16 19:16:18,067 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:18] "GET /web/image?model=res.company&id=1&field=logo&unique=1752693377676 HTTP/1.1" 200 - 5 0.006 0.012
2025-07-16 19:16:24,465 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:24] "GET /partner_autocomplete/static/lib/jsvat.js HTTP/1.1" 200 - 0 0.000 0.004
2025-07-16 19:16:24,766 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:24,811 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:25,049 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:25,561 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.510162 seconds 
2025-07-16 19:16:25,567 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.754664 seconds 
2025-07-16 19:16:25,571 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.800446 seconds 
2025-07-16 19:16:25,595 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:25] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 18 0.042 0.545
2025-07-16 19:16:25,599 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:25] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 18 0.027 0.780
2025-07-16 19:16:25,606 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:25] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 31 0.044 0.845
2025-07-16 19:16:25,691 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:26,180 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:26,393 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.700157 seconds 
2025-07-16 19:16:26,407 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:26] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 18 0.012 0.721
2025-07-16 19:16:27,102 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.920442 seconds 
2025-07-16 19:16:27,136 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name 
2025-07-16 19:16:27,142 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:27] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 21 0.027 0.951
2025-07-16 19:16:28,081 13928 INFO my_odoo_erp odoo.addons.iap.tools.iap_tools: iap jsonrpc https://partner-autocomplete.odoo.com/api/dnb/1/search_by_name answered in 0.942216 seconds 
2025-07-16 19:16:28,084 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:28] "POST /web/dataset/call_kw/res.partner/autocomplete_by_name HTTP/1.1" 200 - 10 0.011 0.969
2025-07-16 19:16:29,113 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:29] "POST /web/dataset/call_kw/res.company/onchange HTTP/1.1" 200 - 11 0.006 0.016
2025-07-16 19:16:43,235 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:43] "POST /web/dataset/call_kw/res.currency/name_search HTTP/1.1" 200 - 3 0.004 0.006
2025-07-16 19:16:45,861 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:45] "POST /web/dataset/call_kw/res.currency/get_views HTTP/1.1" 200 - 16 0.010 0.022
2025-07-16 19:16:46,119 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:16:46] "POST /web/dataset/call_kw/res.currency/web_search_read HTTP/1.1" 200 - 10 0.010 0.021
2025-07-16 19:17:21,529 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:21] "POST /web/dataset/call_kw/res.currency/web_search_read HTTP/1.1" 200 - 6 0.008 0.012
2025-07-16 19:17:23,664 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:23] "POST /web/dataset/call_kw/res.currency/web_read HTTP/1.1" 200 - 2 0.001 0.005
2025-07-16 19:17:29,887 13928 INFO my_odoo_erp odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'templates'] 
2025-07-16 19:17:29,889 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:29] "POST /web/dataset/call_kw/res.company/web_save HTTP/1.1" 200 - 220 0.162 0.203
2025-07-16 19:17:30,258 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "POST /web/dataset/call_kw/res.company/read HTTP/1.1" 200 - 3 0.020 0.017
2025-07-16 19:17:30,303 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "POST /mail/thread/data HTTP/1.1" 200 - 25 0.071 0.030
2025-07-16 19:17:30,317 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "POST /mail/thread/messages HTTP/1.1" 200 - 28 0.084 0.034
2025-07-16 19:17:30,360 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "POST /web/session/get_session_info HTTP/1.1" 200 - 52 0.087 0.074
2025-07-16 19:17:30,559 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "GET /web?reload=true HTTP/1.1" 200 - 26 0.019 0.069
2025-07-16 19:17:30,818 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:30] "POST /onboarding/account_invoice HTTP/1.1" 200 - 34 0.045 0.202
2025-07-16 19:17:31,099 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.007 0.032
2025-07-16 19:17:31,432 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /web/action/load HTTP/1.1" 200 - 10 0.016 0.016
2025-07-16 19:17:31,450 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 4 0.011 0.019
2025-07-16 19:17:31,491 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.011 0.024
2025-07-16 19:17:31,523 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /mail/init_messaging HTTP/1.1" 200 - 49 0.067 0.055
2025-07-16 19:17:31,670 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /web/dataset/call_kw/account.move/get_views HTTP/1.1" 200 - 93 0.045 0.125
2025-07-16 19:17:31,788 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.004 0.017
2025-07-16 19:17:31,848 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:31] "POST /web/dataset/call_kw/account.move/web_search_read HTTP/1.1" 200 - 2 0.001 0.005
2025-07-16 19:17:32,004 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.002 0.004
2025-07-16 19:17:32,008 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.003 0.007
2025-07-16 19:17:32,344 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "POST /onboarding/account_invoice HTTP/1.1" 200 - 12 0.006 0.018
2025-07-16 19:17:32,435 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.009
2025-07-16 19:17:32,587 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /web/image/onboarding.onboarding.step/2/step_image HTTP/1.1" 304 - 5 0.003 0.008
2025-07-16 19:17:32,727 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /web/image/onboarding.onboarding.step/11/step_image HTTP/1.1" 304 - 4 0.004 0.012
2025-07-16 19:17:32,730 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /web/image/onboarding.onboarding.step/3/step_image HTTP/1.1" 304 - 4 0.006 0.014
2025-07-16 19:17:32,732 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:32] "GET /web/image/onboarding.onboarding.step/4/step_image HTTP/1.1" 304 - 4 0.006 0.015
2025-07-16 19:17:34,270 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:34] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.003
2025-07-16 19:17:35,418 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:35] "POST /web/dataset/call_kw/onboarding.onboarding.step/action_open_step_base_document_layout HTTP/1.1" 200 - 3 0.001 0.003
2025-07-16 19:17:35,694 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:35] "POST /web/dataset/call_kw/base.document.layout/get_views HTTP/1.1" 200 - 10 0.005 0.015
2025-07-16 19:17:35,922 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:35] "POST /web/dataset/call_kw/base.document.layout/onchange HTTP/1.1" 200 - 52 0.031 0.157
2025-07-16 19:17:36,022 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:36] "POST /web/dataset/call_kw/report.layout/web_search_read HTTP/1.1" 200 - 2 0.002 0.007
2025-07-16 19:17:36,268 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:36] "POST /web/dataset/call_kw/report.paperformat/name_search HTTP/1.1" 200 - 3 0.004 0.008
2025-07-16 19:17:36,320 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:36] "GET /web/bundle/web_editor.backend_assets_wysiwyg?lang=en_US HTTP/1.1" 200 - 7 0.005 0.058
2025-07-16 19:17:37,141 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/ed27a86/web_editor.backend_assets_wysiwyg.min.css (id:450) 
2025-07-16 19:17:37,174 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:37] "GET /web/assets/ed27a86/web_editor.backend_assets_wysiwyg.min.css HTTP/1.1" 200 - 11 0.011 0.838
2025-07-16 19:17:37,698 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/5438022/web_editor.backend_assets_wysiwyg.min.js (id:451) 
2025-07-16 19:17:37,847 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:37] "GET /web/assets/5438022/web_editor.backend_assets_wysiwyg.min.js HTTP/1.1" 200 - 8 0.005 0.718
2025-07-16 19:17:38,228 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:38] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 8 0.006 0.018
2025-07-16 19:17:43,560 13928 INFO my_odoo_erp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/621c608/web.report_assets_common.min.css (id:452) 
2025-07-16 19:17:43,589 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:43] "GET /web/assets/621c608/web.report_assets_common.min.css HTTP/1.1" 200 - 10 0.018 5.107
2025-07-16 19:17:43,629 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:43] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.003
2025-07-16 19:17:43,944 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:43] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.002
2025-07-16 19:17:49,084 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:49] "POST /web/dataset/call_kw/base.document.layout/onchange HTTP/1.1" 200 - 3 0.002 0.014
2025-07-16 19:17:49,687 13928 INFO my_odoo_erp odoo.modules.registry: Caches invalidated, signaling through the database: ['assets'] 
2025-07-16 19:17:49,688 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:49] "POST /web/dataset/call_kw/base.document.layout/web_save HTTP/1.1" 200 - 77 0.045 0.314
2025-07-16 19:17:49,737 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:49] "POST /web/dataset/call_button HTTP/1.1" 200 - 18 0.015 0.017
2025-07-16 19:17:50,053 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:50] "GET /web/image?model=base.document.layout&id=1&field=logo&unique=************* HTTP/1.1" 200 - 7 0.011 0.016
2025-07-16 19:17:50,098 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:50] "POST /onboarding/account_invoice HTTP/1.1" 200 - 26 0.023 0.052
2025-07-16 19:17:50,117 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:50] "GET /base/static/img/onboarding_confetti.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-07-16 19:17:52,857 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:52] "POST /web/dataset/call_kw/onboarding.onboarding.step/action_open_step_create_invoice HTTP/1.1" 200 - 3 0.002 0.004
2025-07-16 19:17:53,182 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:53] "POST /web/dataset/call_kw/account.move/get_views HTTP/1.1" 200 - 8 0.008 0.061
2025-07-16 19:17:53,295 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:53] "POST /web/dataset/call_kw/account.move/onchange HTTP/1.1" 200 - 33 0.022 0.076
2025-07-16 19:17:53,976 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:17:53] "GET /web/image?field=avatar_128&id=3&model=res.partner&unique=2025-07-16%2019:17:29 HTTP/1.1" 200 - 7 0.004 0.013
2025-07-16 19:18:03,931 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:03] "POST /web/dataset/call_kw/account.move/web_search_read HTTP/1.1" 200 - 2 0.001 0.006
2025-07-16 19:18:04,288 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:04] "POST /onboarding/account_invoice HTTP/1.1" 200 - 13 0.007 0.019
2025-07-16 19:18:18,723 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:18] "POST /web/action/load HTTP/1.1" 200 - 8 0.005 0.012
2025-07-16 19:18:20,089 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:20] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 381 0.137 0.916
2025-07-16 19:18:20,468 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:20] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 328 0.128 0.237
2025-07-16 19:18:20,866 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:20] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.001 0.003
2025-07-16 19:18:20,900 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:20] "POST /base_setup/data HTTP/1.1" 200 - 6 0.004 0.004
2025-07-16 19:18:21,133 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:21] "GET /base/static/description/settings.png HTTP/1.1" 200 - 0 0.000 0.009
2025-07-16 19:18:21,244 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:21] "GET /account/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.011
2025-07-16 19:18:21,245 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:21] "GET /base_setup/static/src/img/google_play.png HTTP/1.1" 200 - 0 0.000 0.009
2025-07-16 19:18:21,257 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:21] "GET /base_setup/static/src/img/app_store.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-16 19:18:33,302 13928 INFO my_odoo_erp odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-07-16 19:18:33,303 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:33] "POST /web/dataset/call_kw/res.config.settings/web_save HTTP/1.1" 200 - 315 0.145 0.272
2025-07-16 19:18:33,655 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:33] "POST /web/action/load HTTP/1.1" 200 - 14 0.009 0.017
2025-07-16 19:18:34,334 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:34] "POST /web/dataset/call_kw/res.company/get_views HTTP/1.1" 200 - 408 0.155 0.291
2025-07-16 19:18:34,356 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:34] "POST /web/dataset/call_kw/res.company/web_search_read HTTP/1.1" 200 - 11 0.004 0.011
2025-07-16 19:18:34,731 13928 INFO ? werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:34] "GET /web/static/img/openhand.cur HTTP/1.1" 200 - 0 0.000 0.014
2025-07-16 19:18:48,546 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:48] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 329 0.139 0.279
2025-07-16 19:18:54,022 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:54] "POST /web/dataset/call_kw/res.config.settings/web_save HTTP/1.1" 200 - 301 0.121 0.232
2025-07-16 19:18:54,039 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:54] "POST /web/dataset/call_button HTTP/1.1" 200 - 1 0.001 0.004
2025-07-16 19:18:54,709 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:54] "POST /web/dataset/call_kw/res.company/get_views HTTP/1.1" 200 - 252 0.098 0.251
2025-07-16 19:18:54,744 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:54] "POST /web/dataset/call_kw/res.company/web_read HTTP/1.1" 200 - 16 0.008 0.019
2025-07-16 19:18:55,161 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:55] "POST /mail/thread/data HTTP/1.1" 200 - 21 0.031 0.031
2025-07-16 19:18:55,175 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:55] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.027 0.030
2025-07-16 19:18:57,087 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:18:57] "POST /web/dataset/call_kw/res.country/name_search HTTP/1.1" 200 - 3 0.004 0.007
2025-07-16 19:19:00,877 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:00] "POST /web/dataset/call_kw/res.country/get_views HTTP/1.1" 200 - 14 0.009 0.019
2025-07-16 19:19:01,123 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:01] "POST /web/dataset/call_kw/res.country/web_search_read HTTP/1.1" 200 - 3 0.002 0.008
2025-07-16 19:19:23,998 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:23] "POST /web/dataset/call_kw/res.country/web_search_read HTTP/1.1" 200 - 2 0.003 0.005
2025-07-16 19:19:29,434 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:29] "POST /web/dataset/call_kw/res.country/web_read HTTP/1.1" 200 - 2 0.001 0.004
2025-07-16 19:19:29,717 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:29] "POST /web/dataset/call_kw/res.company/onchange HTTP/1.1" 200 - 12 0.007 0.021
2025-07-16 19:19:41,225 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:41] "POST /web/dataset/call_kw/res.company/web_save HTTP/1.1" 200 - 46 0.032 0.074
2025-07-16 19:19:41,579 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:41] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.013 0.018
2025-07-16 19:19:41,605 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:41] "POST /mail/thread/data HTTP/1.1" 200 - 10 0.028 0.026
2025-07-16 19:19:41,614 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:41] "GET /web/image?model=res.company&id=1&field=logo&unique=1752693581236 HTTP/1.1" 200 - 5 0.010 0.032
2025-07-16 19:19:41,677 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:41] "POST /web/session/get_session_info HTTP/1.1" 200 - 42 0.058 0.073
2025-07-16 19:19:42,213 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:42] "GET /web HTTP/1.1" 200 - 34 0.018 0.389
2025-07-16 19:19:42,545 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:42] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.019 0.017
2025-07-16 19:19:42,873 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:42] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 4 0.023 0.013
2025-07-16 19:19:42,982 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:42] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.037 0.020
2025-07-16 19:19:42,991 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:42] "POST /mail/init_messaging HTTP/1.1" 200 - 43 0.087 0.069
2025-07-16 19:19:43,006 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:43] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.009 0.032
2025-07-16 19:19:43,290 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:43] "POST /web/dataset/call_kw/res.company/get_views HTTP/1.1" 200 - 252 0.125 0.266
2025-07-16 19:19:43,312 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:43] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.002 0.005
2025-07-16 19:19:43,367 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:43] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.017
2025-07-16 19:19:43,637 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:43] "POST /web/dataset/call_kw/res.company/web_read HTTP/1.1" 200 - 12 0.008 0.024
2025-07-16 19:19:44,170 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:44] "POST /mail/thread/data HTTP/1.1" 200 - 10 0.020 0.045
2025-07-16 19:19:44,218 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:44] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.027 0.035
2025-07-16 19:19:44,234 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:44] "GET /web/image?model=res.company&id=1&field=logo&unique=1752693583676 HTTP/1.1" 200 - 5 0.003 0.014
2025-07-16 19:19:45,790 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:45] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.005
2025-07-16 19:19:48,174 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:48] "POST /web/action/load HTTP/1.1" 200 - 8 0.004 0.009
2025-07-16 19:19:48,786 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:48] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 268 0.105 0.275
2025-07-16 19:19:49,115 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:49] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 265 0.094 0.221
2025-07-16 19:19:49,501 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:49] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.003 0.006
2025-07-16 19:19:49,517 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:19:49] "POST /base_setup/data HTTP/1.1" 200 - 6 0.003 0.005
2025-07-16 19:20:04,254 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:04] "GET /web?debug=1 HTTP/1.1" 200 - 27 0.023 0.102
2025-07-16 19:20:04,622 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:04] "GET /web/webclient/load_menus/3994c05d1d625242da16a2dea445dd6fdde182197022c4affa7f9ca2fd111493 HTTP/1.1" 200 - 1 0.001 0.169
2025-07-16 19:20:04,852 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:04] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 2 0.003 0.015
2025-07-16 19:20:04,876 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:04] "POST /web/action/load HTTP/1.1" 200 - 6 0.022 0.017
2025-07-16 19:20:04,900 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:04] "POST /mail/init_messaging HTTP/1.1" 200 - 32 0.034 0.049
2025-07-16 19:20:05,021 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.002 0.008
2025-07-16 19:20:05,047 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.007 0.011
2025-07-16 19:20:05,054 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.008 0.015
2025-07-16 19:20:05,257 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.008 0.030
2025-07-16 19:20:05,297 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "GET /web/image/res.partner/2/avatar_128?unique=2025-07-16%2019:17:29 HTTP/1.1" 200 - 7 0.004 0.015
2025-07-16 19:20:05,305 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.003 0.008
2025-07-16 19:20:05,679 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:05] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.015
2025-07-16 19:20:06,926 13928 INFO my_odoo_erp werkzeug: 127.0.0.1 - - [16/Jul/2025 19:20:06] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.003
2025-07-16 19:20:46,316 980 WARNING ? py.warnings: D:\my_odoo_erp\odoo\tools\config.py:579: DeprecationWarning: The longpolling-port is a deprecated alias to the gevent-port option, please use the latter.
  File "D:\my_odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "D:\my_odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 186, in run
    main(args)
  File "D:\my_odoo_erp\odoo\cli\server.py", line 133, in main
    odoo.tools.config.parse_config(args)
  File "D:\my_odoo_erp\odoo\tools\config.py", line 390, in parse_config
    self._warn_deprecated_options()
  File "D:\my_odoo_erp\odoo\tools\config.py", line 579, in _warn_deprecated_options
    warnings.warn(
 
2025-07-16 19:20:46,317 980 INFO ? odoo: Odoo version 17.0 
2025-07-16 19:20:46,317 980 INFO ? odoo: Using configuration file at D:\my_odoo_erp\odoo.conf 
2025-07-16 19:20:46,318 980 INFO ? odoo: addons paths: ['D:\\my_odoo_erp\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\17.0', 'd:\\my_odoo_erp\\addons', 'd:\\my_odoo_erp\\custom_addons', 'd:\\my_odoo_erp\\account_financial_tools', 'd:\\my_odoo_erp\\account_invoicing', 'd:\\my_odoo_erp\\odoo\\addons'] 
2025-07-16 19:20:46,318 980 INFO ? odoo: database: odoo@localhost:5432 
2025-07-16 19:20:46,318 980 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-16 19:20:46,709 980 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-16 19:20:47,485 980 INFO my_odoo_erp odoo.modules.loading: loading 1 modules... 
2025-07-16 19:20:47,497 980 INFO my_odoo_erp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-16 19:20:47,547 980 INFO my_odoo_erp odoo.modules.loading: updating modules list 
2025-07-16 19:20:47,553 980 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-16 19:20:49,765 980 INFO my_odoo_erp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Check Printing Base', 'Debit Notes', 'Import/Export Invoices From XML/PDF', 'Define Taxes as Python Code', 'Odoo 17 Budget Management'] to user __system__ #1 via n/a 
2025-07-16 19:20:49,850 980 INFO my_odoo_erp odoo.modules.loading: loading 42 modules... 
2025-07-16 19:20:50,358 980 INFO my_odoo_erp odoo.modules.loading: Loading module analytic (16/42) 
2025-07-16 19:20:50,509 980 INFO my_odoo_erp odoo.modules.registry: module analytic: creating or updating database tables 
2025-07-16 19:20:50,725 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/analytic_security.xml 
2025-07-16 19:20:50,851 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/security/ir.model.access.csv 
2025-07-16 19:20:50,899 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_line_views.xml 
2025-07-16 19:20:51,023 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_account_views.xml 
2025-07-16 19:20:51,111 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_plan_views.xml 
2025-07-16 19:20:51,165 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [21, 22] 
2025-07-16 19:20:51,173 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/views/analytic_distribution_model_views.xml 
2025-07-16 19:20:51,203 980 INFO my_odoo_erp odoo.modules.loading: loading analytic/data/analytic_data.xml 
2025-07-16 19:20:51,349 980 INFO my_odoo_erp odoo.modules.loading: Module analytic loaded in 0.99s, 333 queries (+333 other) 
2025-07-16 19:20:51,734 980 INFO my_odoo_erp odoo.modules.loading: Loading module account (35/42) 
2025-07-16 19:20:52,098 980 INFO my_odoo_erp odoo.modules.registry: module account: creating or updating database tables 
2025-07-16 19:20:52,908 980 INFO my_odoo_erp odoo.modules.loading: loading account/security/account_security.xml 
2025-07-16 19:20:53,320 980 INFO my_odoo_erp odoo.modules.loading: loading account/security/ir.model.access.csv 
2025-07-16 19:20:53,573 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_data.xml 
2025-07-16 19:20:53,603 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [1] 
2025-07-16 19:20:53,620 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [2] 
2025-07-16 19:20:53,641 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [3] 
2025-07-16 19:20:53,653 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [4] 
2025-07-16 19:20:53,667 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [5] 
2025-07-16 19:20:53,681 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [6] 
2025-07-16 19:20:53,696 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [7] 
2025-07-16 19:20:53,709 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [8, 9] 
2025-07-16 19:20:53,724 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted account.payment.term.line records with IDs: [10] 
2025-07-16 19:20:53,758 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/digest_data.xml 
2025-07-16 19:20:53,772 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_report.xml 
2025-07-16 19:20:53,794 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/mail_template_data.xml 
2025-07-16 19:20:53,826 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/onboarding_data.xml 
2025-07-16 19:20:53,926 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_view.xml 
2025-07-16 19:20:54,056 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_automatic_entry_wizard_views.xml 
2025-07-16 19:20:54,077 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_unreconcile_view.xml 
2025-07-16 19:20:54,093 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_reversal_view.xml 
2025-07-16 19:20:54,109 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_resequence_views.xml 
2025-07-16 19:20:54,126 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_payment_register_views.xml 
2025-07-16 19:20:54,145 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_move_views.xml 
2025-07-16 19:20:54,820 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/setup_wizards_view.xml 
2025-07-16 19:20:54,864 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_views.xml 
2025-07-16 19:20:54,917 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_group_views.xml 
2025-07-16 19:20:54,944 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_views.xml 
2025-07-16 19:20:55,050 980 INFO my_odoo_erp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [29, 30, 31] 
2025-07-16 19:20:55,077 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_account_tag_views.xml 
2025-07-16 19:20:55,103 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_bank_statement_views.xml 
2025-07-16 19:20:55,151 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_reconcile_model_views.xml 
2025-07-16 19:20:55,203 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_tax_views.xml 
2025-07-16 19:20:55,316 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_full_reconcile_views.xml 
2025-07-16 19:20:55,327 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_term_views.xml 
2025-07-16 19:20:55,372 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_payment_method.xml 
2025-07-16 19:20:55,392 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_partner_bank_views.xml 
2025-07-16 19:20:55,434 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_statement.xml 
2025-07-16 19:20:55,465 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/terms_template.xml 
2025-07-16 19:20:55,497 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_validate_move_view.xml 
2025-07-16 19:20:55,516 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_company_views.xml 
2025-07-16 19:20:55,562 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/product_view.xml 
2025-07-16 19:20:55,614 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_plan_views.xml 
2025-07-16 19:20:55,627 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_account_views.xml 
2025-07-16 19:20:55,660 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_distribution_model_views.xml 
2025-07-16 19:20:55,683 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_analytic_line_views.xml 
2025-07-16 19:20:55,750 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_invoice.xml 
2025-07-16 19:20:55,862 980 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_invoice_report_view.xml 
2025-07-16 19:20:55,933 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_cash_rounding_view.xml 
2025-07-16 19:20:55,965 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/ir_module_views.xml 
2025-07-16 19:20:55,980 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_config_settings_views.xml 
2025-07-16 19:20:56,062 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/partner_view.xml 
2025-07-16 19:20:56,197 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_journal_dashboard_view.xml 
2025-07-16 19:20:56,235 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_portal_templates.xml 
2025-07-16 19:20:56,336 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/report_payment_receipt_templates.xml 
2025-07-16 19:20:56,363 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/service_cron.xml 
2025-07-16 19:20:56,395 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_incoterms_view.xml 
2025-07-16 19:20:56,426 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_incoterms_data.xml 
2025-07-16 19:20:56,445 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/digest_views.xml 
2025-07-16 19:20:56,462 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_move_send_views.xml 
2025-07-16 19:20:56,482 980 INFO my_odoo_erp odoo.modules.loading: loading account/report/account_hash_integrity_templates.xml 
2025-07-16 19:20:56,499 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/res_currency.xml 
2025-07-16 19:20:56,518 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/account_menuitem.xml 
2025-07-16 19:20:56,839 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/account_tour_upload_bill.xml 
2025-07-16 19:20:56,859 980 INFO my_odoo_erp odoo.modules.loading: loading account/wizard/accrued_orders.xml 
2025-07-16 19:20:56,872 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/bill_preview_template.xml 
2025-07-16 19:20:56,893 980 INFO my_odoo_erp odoo.modules.loading: loading account/data/account_reports_data.xml 
2025-07-16 19:20:56,916 980 INFO my_odoo_erp odoo.modules.loading: loading account/views/uom_uom_views.xml 
2025-07-16 19:20:57,039 980 INFO my_odoo_erp odoo.modules.loading: Module account loaded in 5.31s, 4331 queries (+4331 other) 
2025-07-16 19:20:57,048 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_edi_ubl_cii (37/42) 
2025-07-16 19:20:57,440 980 INFO my_odoo_erp odoo.modules.registry: module account_edi_ubl_cii: creating or updating database tables 
2025-07-16 19:20:57,644 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/cii_22_templates.xml 
2025-07-16 19:20:57,732 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_20_templates.xml 
2025-07-16 19:20:57,996 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/data/ubl_21_templates.xml 
2025-07-16 19:20:58,072 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_config_settings_views.xml 
2025-07-16 19:20:58,119 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/views/res_partner_views.xml 
2025-07-16 19:20:58,182 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi_ubl_cii/wizard/account_move_send_views.xml 
2025-07-16 19:20:58,214 980 INFO my_odoo_erp odoo.modules.loading: Module account_edi_ubl_cii loaded in 1.17s, 654 queries (+654 other) 
2025-07-16 19:20:58,214 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment (38/42) 
2025-07-16 19:20:58,462 980 INFO my_odoo_erp odoo.modules.registry: module account_payment: creating or updating database tables 
2025-07-16 19:20:58,634 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/ir_config_parameter.xml 
2025-07-16 19:20:58,658 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/data/onboarding_data.xml 
2025-07-16 19:20:58,675 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir.model.access.csv 
2025-07-16 19:20:58,701 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/security/ir_rules.xml 
2025-07-16 19:20:58,713 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_menus.xml 
2025-07-16 19:20:58,743 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_portal_templates.xml 
2025-07-16 19:20:58,816 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_move_views.xml 
2025-07-16 19:20:58,857 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_journal_views.xml 
2025-07-16 19:20:58,879 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/account_payment_views.xml 
2025-07-16 19:20:58,902 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_provider_views.xml 
2025-07-16 19:20:58,921 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/views/payment_transaction_views.xml 
2025-07-16 19:20:58,944 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/account_payment_register_views.xml 
2025-07-16 19:20:58,961 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_link_wizard_views.xml 
2025-07-16 19:20:58,967 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/payment_refund_wizard_views.xml 
2025-07-16 19:20:58,979 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment/wizards/res_config_settings_views.xml 
2025-07-16 19:20:59,066 980 INFO my_odoo_erp odoo.modules.loading: Module account_payment loaded in 0.85s, 475 queries (+475 other) 
2025-07-16 19:20:59,066 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_payment_term (39/42) 
2025-07-16 19:20:59,392 980 INFO my_odoo_erp odoo.modules.registry: module account_payment_term: creating or updating database tables 
2025-07-16 19:20:59,425 980 INFO my_odoo_erp odoo.modules.loading: loading account_payment_term/views/account_payment_term_views.xml 
2025-07-16 19:20:59,476 980 INFO my_odoo_erp odoo.modules.loading: Module account_payment_term loaded in 0.41s, 48 queries (+48 other) 
2025-07-16 19:20:59,476 980 INFO my_odoo_erp odoo.modules.loading: Loading module snailmail_account (40/42) 
2025-07-16 19:20:59,716 980 INFO my_odoo_erp odoo.modules.registry: module snailmail_account: creating or updating database tables 
2025-07-16 19:21:00,021 980 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/views/res_config_settings_views.xml 
2025-07-16 19:21:00,108 980 INFO my_odoo_erp odoo.modules.loading: loading snailmail_account/wizard/account_move_send_views.xml 
2025-07-16 19:21:00,141 980 INFO my_odoo_erp odoo.modules.loading: Module snailmail_account loaded in 0.67s, 155 queries (+155 other) 
2025-07-16 19:21:00,141 980 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_account (41/42) 
2025-07-16 19:21:00,383 980 INFO my_odoo_erp odoo.modules.registry: module spreadsheet_account: creating or updating database tables 
2025-07-16 19:21:00,476 980 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_account loaded in 0.34s, 54 queries (+54 other) 
2025-07-16 19:21:00,477 980 INFO my_odoo_erp odoo.modules.loading: Loading module spreadsheet_dashboard_account (42/42) 
2025-07-16 19:21:00,746 980 INFO my_odoo_erp odoo.modules.loading: loading spreadsheet_dashboard_account/data/dashboards.xml 
2025-07-16 19:21:00,798 980 INFO my_odoo_erp odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.32s, 28 queries (+28 other) 
2025-07-16 19:21:00,798 980 INFO my_odoo_erp odoo.modules.loading: 42 modules loaded in 10.95s, 6078 queries (+6078 extra) 
2025-07-16 19:21:00,800 980 INFO my_odoo_erp odoo.modules.loading: loading 47 modules... 
2025-07-16 19:21:00,800 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_check_printing (37/47) 
2025-07-16 19:21:00,976 980 INFO my_odoo_erp odoo.modules.registry: module account_check_printing: creating or updating database tables 
2025-07-16 19:21:01,338 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/security/ir.model.access.csv 
2025-07-16 19:21:01,354 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/data/account_check_printing_data.xml 
2025-07-16 19:21:01,387 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/views/account_journal_views.xml 
2025-07-16 19:21:01,411 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/views/account_move_views.xml 
2025-07-16 19:21:01,423 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/views/account_payment_views.xml 
2025-07-16 19:21:01,449 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/views/res_config_settings_views.xml 
2025-07-16 19:21:01,475 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/views/res_partner_views.xml 
2025-07-16 19:21:01,494 980 INFO my_odoo_erp odoo.modules.loading: loading account_check_printing/wizard/print_prenumbered_checks_views.xml 
2025-07-16 19:21:01,549 980 INFO my_odoo_erp odoo.modules.loading: Module account_check_printing loaded in 0.75s, 343 queries (+343 other) 
2025-07-16 19:21:01,549 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_debit_note (38/47) 
2025-07-16 19:21:01,704 980 INFO my_odoo_erp odoo.modules.registry: module account_debit_note: creating or updating database tables 
2025-07-16 19:21:01,883 980 INFO my_odoo_erp odoo.modules.loading: loading account_debit_note/wizard/account_debit_note_view.xml 
2025-07-16 19:21:01,904 980 INFO my_odoo_erp odoo.modules.loading: loading account_debit_note/views/account_move_view.xml 
2025-07-16 19:21:01,963 980 INFO my_odoo_erp odoo.modules.loading: loading account_debit_note/security/ir.model.access.csv 
2025-07-16 19:21:02,001 980 INFO my_odoo_erp odoo.modules.loading: Module account_debit_note loaded in 0.45s, 193 queries (+193 other) 
2025-07-16 19:21:02,002 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_edi (39/47) 
2025-07-16 19:21:02,154 980 INFO my_odoo_erp odoo.modules.registry: module account_edi: creating or updating database tables 
2025-07-16 19:21:02,194 980 INFO my_odoo_erp odoo.models: Prepare computation of account.journal.edi_format_ids 
2025-07-16 19:21:02,458 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi/security/ir.model.access.csv 
2025-07-16 19:21:02,478 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi/views/account_edi_document_views.xml 
2025-07-16 19:21:02,488 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi/views/account_move_views.xml 
2025-07-16 19:21:02,610 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi/views/account_journal_views.xml 
2025-07-16 19:21:02,624 980 INFO my_odoo_erp odoo.modules.loading: loading account_edi/data/cron.xml 
2025-07-16 19:21:02,665 980 INFO my_odoo_erp odoo.modules.loading: Module account_edi loaded in 0.66s, 320 queries (+320 other) 
2025-07-16 19:21:02,665 980 INFO my_odoo_erp odoo.modules.loading: Loading module account_tax_python (43/47) 
2025-07-16 19:21:02,809 980 INFO my_odoo_erp odoo.modules.registry: module account_tax_python: creating or updating database tables 
2025-07-16 19:21:02,876 980 INFO my_odoo_erp odoo.modules.loading: loading account_tax_python/views/account_tax_views.xml 
2025-07-16 19:21:02,908 980 INFO my_odoo_erp odoo.modules.loading: Module account_tax_python loaded in 0.24s, 58 queries (+58 other) 
2025-07-16 19:21:02,908 980 INFO my_odoo_erp odoo.modules.loading: Loading module base_account_budget (44/47) 
2025-07-16 19:21:02,917 980 WARNING my_odoo_erp odoo.api.create: The model odoo.addons.base_account_budget.models.account_budget is not overriding the create method in batch 
2025-07-16 19:21:03,053 980 INFO my_odoo_erp odoo.modules.registry: module base_account_budget: creating or updating database tables 
2025-07-16 19:21:03,307 980 INFO my_odoo_erp odoo.modules.loading: loading base_account_budget/security/account_budget_security.xml 
2025-07-16 19:21:03,373 980 INFO my_odoo_erp odoo.modules.loading: loading base_account_budget/security/ir.model.access.csv 
2025-07-16 19:21:03,396 980 INFO my_odoo_erp odoo.modules.loading: loading base_account_budget/views/account_analytic_account_views.xml 
2025-07-16 19:21:03,411 980 INFO my_odoo_erp odoo.modules.loading: loading base_account_budget/views/account_budget_views.xml 
2025-07-16 19:21:03,465 980 WARNING my_odoo_erp odoo.addons.base.models.ir_ui_view: <img> tag must contain an alt attribute
View error context:
{'file': 'd:\\my_odoo_erp\\addons\\base_account_budget\\views\\account_budget_views.xml',
 'line': 31,
 'name': 'budget.kanban',
 'view': ir.ui.view(852,),
 'view.model': 'budget.budget',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_budget_kanban'} 
2025-07-16 19:21:03,532 980 INFO my_odoo_erp odoo.modules.loading: Module base_account_budget loaded in 0.62s, 325 queries (+325 other) 
2025-07-16 19:21:03,532 980 INFO my_odoo_erp odoo.modules.loading: 47 modules loaded in 2.73s, 1239 queries (+1239 extra) 
2025-07-16 19:21:04,023 980 INFO my_odoo_erp odoo.modules.registry: verifying fields for every extended model 
2025-07-16 19:21:04,951 980 INFO my_odoo_erp odoo.modules.loading: Modules loaded. 
2025-07-16 19:21:05,961 980 INFO my_odoo_erp odoo.modules.registry: Registry loaded in 18.579s 
2025-07-16 19:21:05,962 980 INFO my_odoo_erp odoo.service.server: Initiating shutdown 
2025-07-16 19:21:05,962 980 INFO my_odoo_erp odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-16 19:21:05,962 980 INFO my_odoo_erp odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 1 connections  
