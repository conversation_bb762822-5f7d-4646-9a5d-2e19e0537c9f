<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="report_ifrs_statement_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="text-center">
                                <h2>
                                    <strong t-field="doc.company_id.name"/>
                                </h2>
                                <h3 t-field="doc.name"/>
                                <p>
                                    For the period from <span t-field="doc.date_from"/> to <span t-field="doc.date_to"/>
                                </p>
                                <p>
                                    (All amounts in <span t-field="doc.currency_id.name"/>)
                                </p>
                            </div>

                            <br/>

                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th style="width: 60%;">Description</th>
                                        <th class="text-right" style="width: 15%;">Note</th>
                                        <th class="text-right" style="width: 12.5%;">
                                            <span t-field="doc.date_to" t-options="{'format': 'yyyy'}"/>
                                        </th>
                                        <th class="text-right" style="width: 12.5%;" t-if="doc.comparative_period">
                                            <span t-field="doc.comparative_date_to" t-options="{'format': 'yyyy'}"/>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="doc.statement_line_ids" t-as="line">
                                        <tr t-att-class="'ifrs_statement_' + line.line_type">
                                            <td>
                                                <span t-att-style="'padding-left: ' + str(line.indent_level * 20) + 'px;'"
                                                      t-att-class="('font-weight-bold' if line.bold else '') + (' font-italic' if line.italic else '') + (' text-decoration-underline' if line.underline else '')">
                                                    <span t-field="line.name"/>
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span t-field="line.note_number"/>
                                            </td>
                                            <td class="text-right">
                                                <span t-if="line.line_type != 'header'" 
                                                      t-field="line.current_amount" 
                                                      t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                            <td class="text-right" t-if="doc.comparative_period">
                                                <span t-if="line.line_type != 'header'" 
                                                      t-field="line.comparative_amount" 
                                                      t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>

                            <br/>

                            <div class="row">
                                <div class="col-6">
                                    <p><strong>Prepared by:</strong> <span t-field="doc.prepared_by.name"/></p>
                                    <p><strong>Date:</strong> <span t-field="doc.preparation_date" t-options="{'format': 'dd/MM/yyyy'}"/></p>
                                </div>
                                <div class="col-6">
                                    <p t-if="doc.reviewed_by"><strong>Reviewed by:</strong> <span t-field="doc.reviewed_by.name"/></p>
                                    <p t-if="doc.approved_by"><strong>Approved by:</strong> <span t-field="doc.approved_by.name"/></p>
                                </div>
                            </div>

                            <div t-if="doc.notes" class="mt-4">
                                <h4>Notes to Financial Statements</h4>
                                <div t-field="doc.notes"/>
                            </div>

                            <!-- Compliance Information -->
                            <div class="mt-4">
                                <h5>IFRS Compliance Status</h5>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Standard</th>
                                            <th>Status</th>
                                            <th>Check Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="doc.compliance_check_ids" t-as="check">
                                            <tr>
                                                <td t-field="check.name"/>
                                                <td>
                                                    <span t-field="check.status" 
                                                          t-att-class="'ifrs_compliance_' + check.status"/>
                                                </td>
                                                <td t-field="check.check_date" t-options="{'format': 'dd/MM/yyyy'}"/>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

    </data>
</odoo>
