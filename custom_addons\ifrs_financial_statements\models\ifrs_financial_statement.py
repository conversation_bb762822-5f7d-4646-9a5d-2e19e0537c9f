# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date
import logging

_logger = logging.getLogger(__name__)


class IFRSFinancialStatement(models.Model):
    _name = 'ifrs.financial.statement'
    _description = 'IFRS Financial Statement'
    _order = 'date_from desc'
    _rec_name = 'name'

    name = fields.Char(
        string='Statement Name',
        required=True,
        default=lambda self: _('IFRS Financial Statement %s') % fields.Date.today()
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        required=True,
        default=lambda self: self.env.company
    )
    
    statement_type = fields.Selection([
        ('balance_sheet', 'Statement of Financial Position (Balance Sheet)'),
        ('income_statement', 'Statement of Comprehensive Income'),
        ('cash_flow', 'Statement of Cash Flows'),
        ('equity_changes', 'Statement of Changes in Equity'),
        ('notes', 'Notes to Financial Statements'),
        ('complete_set', 'Complete Set of Financial Statements')
    ], string='Statement Type', required=True, default='complete_set')
    
    reporting_period = fields.Selection([
        ('quarterly', 'Quarterly'),
        ('half_yearly', 'Half-Yearly'),
        ('annual', 'Annual')
    ], string='Reporting Period', required=True, default='annual')
    
    date_from = fields.Date(
        string='Period From',
        required=True,
        default=lambda self: fields.Date.today().replace(month=1, day=1)
    )
    
    date_to = fields.Date(
        string='Period To',
        required=True,
        default=lambda self: fields.Date.today().replace(month=12, day=31)
    )
    
    comparative_period = fields.Boolean(
        string='Include Comparative Period',
        default=True,
        help="Include previous period figures for comparison"
    )
    
    comparative_date_from = fields.Date(
        string='Comparative Period From',
        compute='_compute_comparative_dates',
        store=True
    )
    
    comparative_date_to = fields.Date(
        string='Comparative Period To',
        compute='_compute_comparative_dates',
        store=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Reporting Currency',
        required=True,
        default=lambda self: self.env.company.currency_id
    )
    
    functional_currency_id = fields.Many2one(
        'res.currency',
        string='Functional Currency',
        required=True,
        default=lambda self: self.env.company.currency_id
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('published', 'Published')
    ], string='Status', default='draft', tracking=True)
    
    ifrs_compliance_level = fields.Selection([
        ('full', 'Full IFRS Compliance'),
        ('ifrs_sme', 'IFRS for SMEs'),
        ('local_gaap', 'Local GAAP with IFRS Adjustments')
    ], string='IFRS Compliance Level', required=True, default='full')
    
    # Statement Lines
    statement_line_ids = fields.One2many(
        'ifrs.statement.line',
        'statement_id',
        string='Statement Lines'
    )
    
    # Compliance and Audit
    compliance_check_ids = fields.One2many(
        'ifrs.compliance.check',
        'statement_id',
        string='Compliance Checks'
    )
    
    auditor_id = fields.Many2one(
        'res.partner',
        string='External Auditor',
        domain=[('is_company', '=', True)]
    )
    
    audit_opinion = fields.Selection([
        ('unqualified', 'Unqualified Opinion'),
        ('qualified', 'Qualified Opinion'),
        ('adverse', 'Adverse Opinion'),
        ('disclaimer', 'Disclaimer of Opinion')
    ], string='Audit Opinion')
    
    # Notes and Documentation
    notes = fields.Html(string='Notes to Financial Statements')
    internal_notes = fields.Text(string='Internal Notes')
    
    # Approval Workflow
    prepared_by = fields.Many2one('res.users', string='Prepared By', default=lambda self: self.env.user)
    reviewed_by = fields.Many2one('res.users', string='Reviewed By')
    approved_by = fields.Many2one('res.users', string='Approved By')
    
    preparation_date = fields.Datetime(string='Preparation Date', default=fields.Datetime.now)
    review_date = fields.Datetime(string='Review Date')
    approval_date = fields.Datetime(string='Approval Date')
    
    @api.depends('date_from', 'date_to', 'reporting_period')
    def _compute_comparative_dates(self):
        for record in self:
            if record.date_from and record.date_to:
                # Calculate comparative period (previous year)
                from_date = record.date_from
                to_date = record.date_to
                
                record.comparative_date_from = from_date.replace(year=from_date.year - 1)
                record.comparative_date_to = to_date.replace(year=to_date.year - 1)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for record in self:
            if record.date_from >= record.date_to:
                raise ValidationError(_('Period From date must be before Period To date.'))
    
    def action_generate_statement(self):
        """Generate IFRS Financial Statement"""
        self.ensure_one()
        self.state = 'in_progress'
        
        # Clear existing lines
        self.statement_line_ids.unlink()
        
        # Generate statement lines based on type
        if self.statement_type == 'balance_sheet':
            self._generate_balance_sheet()
        elif self.statement_type == 'income_statement':
            self._generate_income_statement()
        elif self.statement_type == 'cash_flow':
            self._generate_cash_flow()
        elif self.statement_type == 'equity_changes':
            self._generate_equity_changes()
        elif self.statement_type == 'complete_set':
            self._generate_complete_set()
        
        # Run compliance checks
        self._run_compliance_checks()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('IFRS Financial Statement generated successfully.'),
                'type': 'success',
            }
        }
    
    def _generate_balance_sheet(self):
        """Generate Statement of Financial Position (Balance Sheet)"""
        # This will be implemented with actual account data
        pass
    
    def _generate_income_statement(self):
        """Generate Statement of Comprehensive Income"""
        # This will be implemented with actual account data
        pass
    
    def _generate_cash_flow(self):
        """Generate Statement of Cash Flows"""
        # This will be implemented with actual account data
        pass
    
    def _generate_equity_changes(self):
        """Generate Statement of Changes in Equity"""
        # This will be implemented with actual account data
        pass
    
    def _generate_complete_set(self):
        """Generate Complete Set of Financial Statements"""
        self._generate_balance_sheet()
        self._generate_income_statement()
        self._generate_cash_flow()
        self._generate_equity_changes()
    
    def _run_compliance_checks(self):
        """Run IFRS compliance checks"""
        # Clear existing checks
        self.compliance_check_ids.unlink()
        
        # Create compliance check records
        checks = [
            {'name': 'IAS 1 - Presentation of Financial Statements', 'status': 'passed'},
            {'name': 'IAS 7 - Statement of Cash Flows', 'status': 'passed'},
            {'name': 'IFRS 15 - Revenue Recognition', 'status': 'warning'},
            {'name': 'IFRS 16 - Leases', 'status': 'passed'},
        ]
        
        for check in checks:
            self.env['ifrs.compliance.check'].create({
                'statement_id': self.id,
                'name': check['name'],
                'status': check['status'],
                'check_date': fields.Datetime.now(),
            })
    
    def action_submit_for_review(self):
        """Submit statement for review"""
        self.state = 'review'
        self.review_date = fields.Datetime.now()
    
    def action_approve(self):
        """Approve the financial statement"""
        self.state = 'approved'
        self.approved_by = self.env.user
        self.approval_date = fields.Datetime.now()
    
    def action_publish(self):
        """Publish the financial statement"""
        self.state = 'published'
    
    def action_reset_to_draft(self):
        """Reset to draft state"""
        self.state = 'draft'

    def action_export_pdf(self):
        """Export financial statement to PDF"""
        return self.env.ref('ifrs_financial_statements.action_report_ifrs_statement').report_action(self)

    def action_export_excel(self):
        """Export financial statement to Excel"""
        # This will be implemented for Excel export
        pass
