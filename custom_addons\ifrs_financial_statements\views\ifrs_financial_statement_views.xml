<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- IFRS Financial Statement Form View -->
        <record id="view_ifrs_financial_statement_form" model="ir.ui.view">
            <field name="name">ifrs.financial.statement.form</field>
            <field name="model">ifrs.financial.statement</field>
            <field name="arch" type="xml">
                <form string="IFRS Financial Statement">
                    <header>
                        <button name="action_generate_statement" type="object" 
                                string="Generate Statement" class="btn-primary"
                                attrs="{'invisible': [('state', 'not in', ['draft', 'in_progress'])]}"/>
                        <button name="action_submit_for_review" type="object" 
                                string="Submit for Review" class="btn-secondary"
                                attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                        <button name="action_approve" type="object" 
                                string="Approve" class="btn-success"
                                attrs="{'invisible': [('state', '!=', 'review')]}"
                                groups="ifrs_financial_statements.group_ifrs_manager"/>
                        <button name="action_publish" type="object" 
                                string="Publish" class="btn-success"
                                attrs="{'invisible': [('state', '!=', 'approved')]}"
                                groups="ifrs_financial_statements.group_ifrs_manager"/>
                        <button name="action_reset_to_draft" type="object" 
                                string="Reset to Draft" class="btn-secondary"
                                attrs="{'invisible': [('state', '=', 'draft')]}"
                                groups="ifrs_financial_statements.group_ifrs_manager"/>
                        <button name="action_export_pdf" type="object" 
                                string="Export PDF" class="btn-secondary"
                                attrs="{'invisible': [('state', 'in', ['draft'])]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,review,approved,published"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Financial Statement Name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="company_id" options="{'no_create': True}"/>
                                <field name="statement_type"/>
                                <field name="reporting_period"/>
                                <field name="ifrs_compliance_level"/>
                            </group>
                            <group>
                                <field name="date_from"/>
                                <field name="date_to"/>
                                <field name="comparative_period"/>
                                <field name="currency_id" options="{'no_create': True}"/>
                            </group>
                        </group>
                        
                        <group attrs="{'invisible': [('comparative_period', '=', False)]}">
                            <group>
                                <field name="comparative_date_from"/>
                                <field name="comparative_date_to"/>
                            </group>
                            <group>
                                <field name="functional_currency_id" options="{'no_create': True}"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Statement Lines" name="statement_lines">
                                <field name="statement_line_ids">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="line_type"/>
                                        <field name="statement_section"/>
                                        <field name="current_amount" sum="Total Current"/>
                                        <field name="comparative_amount" sum="Total Comparative"/>
                                        <field name="variance_amount"/>
                                        <field name="ifrs_reference"/>
                                        <field name="note_number"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Compliance Checks" name="compliance">
                                <field name="compliance_check_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="ifrs_standard"/>
                                        <field name="status"/>
                                        <field name="priority"/>
                                        <field name="resolved"/>
                                        <field name="check_date"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Audit Information" name="audit">
                                <group>
                                    <group>
                                        <field name="auditor_id"/>
                                        <field name="audit_opinion"/>
                                    </group>
                                    <group>
                                        <field name="prepared_by"/>
                                        <field name="reviewed_by"/>
                                        <field name="approved_by"/>
                                    </group>
                                </group>
                                <group>
                                    <group>
                                        <field name="preparation_date"/>
                                        <field name="review_date"/>
                                        <field name="approval_date"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Notes" name="notes">
                                <group>
                                    <field name="notes" widget="html"/>
                                </group>
                                <group>
                                    <field name="internal_notes"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- IFRS Financial Statement Tree View -->
        <record id="view_ifrs_financial_statement_tree" model="ir.ui.view">
            <field name="name">ifrs.financial.statement.tree</field>
            <field name="model">ifrs.financial.statement</field>
            <field name="arch" type="xml">
                <tree string="IFRS Financial Statements">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="statement_type"/>
                    <field name="reporting_period"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <field name="state" decoration-success="state=='published'" 
                           decoration-info="state=='approved'" decoration-warning="state=='review'"/>
                    <field name="prepared_by"/>
                </tree>
            </field>
        </record>

        <!-- IFRS Financial Statement Search View -->
        <record id="view_ifrs_financial_statement_search" model="ir.ui.view">
            <field name="name">ifrs.financial.statement.search</field>
            <field name="model">ifrs.financial.statement</field>
            <field name="arch" type="xml">
                <search string="Search IFRS Financial Statements">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="statement_type"/>
                    <field name="prepared_by"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="Under Review" name="review" domain="[('state', '=', 'review')]"/>
                    <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                    <filter string="Published" name="published" domain="[('state', '=', 'published')]"/>
                    <separator/>
                    <filter string="Current Year" name="current_year" 
                            domain="[('date_from', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')),
                                     ('date_to', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}"/>
                        <filter string="Statement Type" name="group_type" context="{'group_by': 'statement_type'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Reporting Period" name="group_period" context="{'group_by': 'reporting_period'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- IFRS Financial Statement Action -->
        <record id="action_ifrs_financial_statement" model="ir.actions.act_window">
            <field name="name">IFRS Financial Statements</field>
            <field name="res_model">ifrs.financial.statement</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_ifrs_financial_statement_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first IFRS Financial Statement
                </p>
                <p>
                    Generate IFRS compliant financial statements for listed companies.
                    This module helps you create professional financial statements that
                    comply with International Financial Reporting Standards.
                </p>
            </field>
        </record>

    </data>
</odoo>
